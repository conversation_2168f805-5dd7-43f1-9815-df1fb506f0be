<script setup>

import CashlessTerminalComponent from "~/components/DemoComponents/CashlessTerminalComponent/CashlessTerminalComponent.vue";
import NfcScreenComponent from "~/components/DemoComponents/NfcScreenComponent/NfcScreenComponent.vue";
import ValidationScreenComponent from "~/components/DemoComponents/ValidationScreenComponent/ValidationScreenComponent.vue";

let validationScreen = ref(false);
let showNfcScan = ref(false);
let showCard = ref(false);
let amount = ref(0);


function payment(paymentAmount){
    showNfcScan.value = true;

    setTimeout(() => {
        showCard.value = true;
    }, 600)

    setTimeout(() => {
        showNfcScan.value  = false;
        amount.value = paymentAmount;
        validationScreen.value  = true;
    }, 1300);
}

function close() {
    showCard.value = false;
    validationScreen.value = false;
    amount.value = 0;
}

</script>

<style lang="sass" scoped>
@use './SimpleSmartphoneComponent.scss' as *
</style>

<template>
    <div class="smartphone-group">
        <div class="mobile-phone" :class="{scan: showCard}">
            <div class="try-me-banner">
                <span> {{ $t('demo.try_me') }} ! </span>
            </div>

            <div class="screen">
                <CashlessTerminalComponent v-if="!validationScreen && !showNfcScan" @payment="payment($event)"></CashlessTerminalComponent>
                <NfcScreenComponent v-else-if="showNfcScan"></NfcScreenComponent>
                <ValidationScreenComponent :amount="amount" @close="close()" v-else></ValidationScreenComponent>
                <!--			<slot></slot>-->
            </div>
        </div>

        <img alt="blank-card" class="blank-card" :class="{show: showCard}" src="/blank-card.svg" />
    </div>
</template>