
.smartphone-group {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 20px 10px 20px 10px;
    perspective: 1000px;

    .try-me-banner  {
        display: flex;
        justify-content: center;
        padding: 8px;
        background: #001cdd;
        color: white;
        font-weight: bold;
        margin: -20px -10px 0 -10px;
        border-radius: 20px 20px 0 0 ;

        span {
            animation: shake-animation 4.72s ease infinite;
            transform-origin: 50% 50%;
        }
    }

    .blank-card {
        position: absolute;
        top: 0;
        left: 50%;
        height: 250px;
        transform: translateX(200%);
        transition: transform .8s cubic-bezier(0.22, 1, 0.36, 1);
        max-width: 90vw;

        &.show {
            transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1);
            transform: translateX(-50%);
        }
    }

    .mobile-phone {
        position: relative;
        display: flex;
        flex-direction: column;
        z-index: 500;
        margin: auto;
        padding: 10px;
        width: 350px;
        height: 600px;
        max-width: 90%;
        box-sizing: border-box;
        max-height: 70vh;
        box-shadow: 0 0 20px #e2e2e2;
        border-radius: 20px;
        background: #f4f6f8;
        transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1);

        &.scan {
            transition: transform .8s cubic-bezier(0.22, 1, 0.36, 1) .2s;
            transform: rotateX(10deg) scale(0.9) translateY(-30px);
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f2;
            border-radius: 20px;
            overflow-y: auto;
        }
    }

}


@keyframes shake-animation {
    0% { transform:translate(0,0) }
    1.78571% { transform:translate(5px,0) }
    3.57143% { transform:translate(0,0) }
    5.35714% { transform:translate(5px,0) }
    7.14286% { transform:translate(0,0) }
    8.92857% { transform:translate(5px,0) }
    10.71429% { transform:translate(0,0) }
    100% { transform:translate(0,0) }
}