<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";
import TextImageContainerComponent from "~/components/TextImageContainerComponent/TextImageContainerComponent.vue";

const i18n = useI18n();

useHead({
    title: i18n.t('hardware.hero.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('hardware.hero.subtitle')
        },
        {
            property: 'og:title',
            content: i18n.t('hardware.hero.title')
        },
        {
            property: 'og:description',
            content: i18n.t('hardware.hero.subtitle')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ]
});
</script>

<style lang="sass">
@use '../pagesSrc/hardware/hardware.scss' as *
</style>

<template>
    <div id="hardware-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="first container primary">
            <div class="content">
                <div class="hero-banner">
                    <div class="left">
                        <div>
                            <span class="title heading-2xl">{{ $t('hardware.hero.title') }}</span>
                        </div>

                        <span class="subtitle heading-sm">{{ $t('hardware.hero.subtitle') }}</span>

                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>
                    </div>

                    <div class="illustration">
                        <NuxtImg format="webp" alt="customizable-rfid-chips" width="2318" height="1833" :src="'/hardware-fr.png'" sizes="xs:420px md:600px" />
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="text-images">
                    <TextImageContainerComponent
                        :small="$t('hardware.features.centralizedManagement.small')"
                        :title="$t('hardware.features.centralizedManagement.title')"
                        :subtitle="$t('hardware.features.centralizedManagement.subtitle')"
                    >
                        <NuxtImg alt="placeholder" format="webp" loading="lazy" width="600" height="450" src="/sunmi/p2-lite-methods.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('hardware.features.transitionToCashless.small')"
                        :title="$t('hardware.features.transitionToCashless.title')"
                        :subtitle="$t('hardware.features.transitionToCashless.subtitle')"
                        :reversed="true"
                    >
                        <NuxtImg alt="placeholder" format="webp" width="600" height="450" src="/pax/A35.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('hardware.features.clearData.small')"
                        :title="$t('hardware.features.clearData.title')"
                        :subtitle="$t('hardware.features.clearData.subtitle')"
                    >
                        <NuxtImg alt="placeholder" format="webp" loading="lazy" width="600" height="450" src="/interfaces.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('hardware.support.titleSmall') }}</span>
                    <span class="heading-xl">{{ $t('hardware.support.title') }}</span>
                </div>

                <div class="rfid-comparison">
                    <div class="column background">
                        <div class="item image-header background">
                            <NuxtImg format="webp" alt="Festival wristband" src="/bracelet-festival-personnalisable.png" sizes="xs:420px md:600px" />
                        </div>
                        <span class="item name background">{{ $t('hardware.support.festivalWristband.name') }}</span>
                        <span class="item property background">{{ $t('hardware.support.festivalWristband.lossRisk') }}</span>
                        <span class="item property background">{{ $t('hardware.support.festivalWristband.waterproof') }}</span>
                        <span class="item property background">{{ $t('hardware.support.festivalWristband.customizable') }}</span>
                        <span class="item property background">{{ $t('hardware.support.festivalWristband.security') }}</span>

                        <NuxtLinkLocale class="button" :to="{name: 'pricing', hash: '#festival-wristband'}">{{ $t('hardware.support.viewPricing') }}</NuxtLinkLocale>
                    </div>

                    <div class="column">
                        <div class="item image-header">
                            <NuxtImg format="webp" alt="Resort wristband" src="/bracelet-resort.png" sizes="xs:420px md:600px" />
                        </div>
                        <span class="item name">{{ $t('hardware.support.resortWristband.name') }}</span>
                        <span class="item property">{{ $t('hardware.support.resortWristband.lossRisk') }}</span>
                        <span class="item property">{{ $t('hardware.support.resortWristband.waterproof') }}</span>
                        <span class="item property">{{ $t('hardware.support.resortWristband.customizable') }}</span>
                        <span class="item property">{{ $t('hardware.support.resortWristband.adaptability') }}</span>

                        <NuxtLinkLocale class="button" :to="{name: 'pricing', hash: '#resort-wristband'}">{{ $t('hardware.support.viewPricing') }}</NuxtLinkLocale>
                    </div>

                    <div class="column background">
                        <div class="item image-header background">
                            <NuxtImg format="webp" loading="lazy" alt="PVC card" src="/carte-cashless.png" sizes="xs:420px md:600px" />
                        </div>
                        <span class="item name background">{{ $t('hardware.support.pvcCard.name') }}</span>
                        <span class="item property background">{{ $t('hardware.support.pvcCard.lossRisk') }}</span>
                        <span class="item property background">{{ $t('hardware.support.pvcCard.waterproof') }}</span>
                        <span class="item property background">{{ $t('hardware.support.pvcCard.customizable') }}</span>
                        <span class="item property background">{{ $t('hardware.support.pvcCard.adaptability') }}</span>

                        <NuxtLinkLocale class="button" :to="{name: 'pricing', hash: '#pvc-card'}">{{ $t('hardware.support.viewPricing') }}</NuxtLinkLocale>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-bubble">
                    <div class="content-title">
                        <span class="heading-xl">{{ $t('hardware.readyToSwitch.title') }}</span>
                    </div>

                    <div class="big-buttons">
                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <NuxtLinkLocale to="/contact" class="grey button">
                            {{ $t('hardware.readyToSwitch.contact') }}
                        </NuxtLinkLocale>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>
