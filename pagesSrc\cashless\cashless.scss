#homepage {
    >.primary {
        background: var(--soft-background-color);
    }

    .hero-banner {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 4rem;
        padding: 4rem 0 6rem 0;

        @media (max-width: 1100px) {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            padding: 2rem 0;
        }

        >.left {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: 1.25rem;

            .title {
                font-weight: 500;

                &.bold {
                    font-weight: 600;
                }
            }

            .subtitle {
                font-size: 1.375rem;
                font-weight: 400;
            }

            .button {
                margin-top: 0.5rem;
                gap: 1rem;

                .hint {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-weight: 400;
                    opacity: 0.8;
                    font-size: .875rem;
                }

                img {
                    height: 16px;
                    filter: invert(100%) sepia(0%);
                }
            }

            .social {
                display: flex;
                align-items: center;
                gap: .75rem;
                margin-top: 1rem;

                .clients {
                    display: flex;

                    img {
                        height: 32px;
                        width: 32px;
                        background-color: #D9D9D9;
                        border-radius: 50%;
                        border: 2px solid var(--soft-background-color);
                        object-fit: cover;

                        &:not(:last-child) {
                            margin-right: -.75rem;
                        }
                    }
                }

                .right {
                    display: flex;
                    flex-direction: column;
                    gap: .3rem;

                    .stars {
                        display: flex;
                        align-items: center;
                        gap: .1rem;

                        img {
                            height: 12px;
                            filter: invert(66%) sepia(57%) saturate(1580%) hue-rotate(9deg) brightness(103%) contrast(97%);
                        }
                    }

                    span {
                        //font-weight: 500;
                    }
                }
            }
        }
    }

    .trusted-by-companies {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        font-weight: 500;
        font-size: 1.125rem;
        color: #4d4e56;

        .companies {
            display: flex;
            justify-content: center;
            gap: 3rem;

            img {
                filter: grayscale(1);
                height: 52px;
            }
        }
    }

    .illustration {
        position: relative;
        overflow: hidden;
    }

    .proof-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 4rem 3rem;
        margin-top: 4rem;

        @media (max-width: 1100px) {
            grid-template-columns: 1fr;
        }

        .proof {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            img {
                height: 32px;
                width: 32px;
                filter: invert(100%) sepia(0%);
            }

            .title {
                font-size: 1.25rem;
                font-weight: 500;
            }

            .description {
                font-size: 1rem;
            }
        }
    }

    .faq {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        max-width: 800px;
        margin: auto;

        .group {
            display: flex;
            flex-direction: column;
            gap: .5rem;
            padding: 1.5rem;
            border: .1rem solid #c5c5c5;
            border-radius: 1rem;
            cursor: pointer;
            user-select: none;

            .question {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 1.25rem;
                line-height: 1.5;
                font-weight: 500;

                img {
                    height: 16px;

                    &.rotated{
                        transform: rotate(180deg);
                    }
                }
            }

            .response {
                display: none;
                color: #4d4e56;

                &.showed {
                    display: initial;
                }
            }
        }
    }

    .lifeline {
        display: flex;
        flex-direction: column;
        max-width: 60rem;
        margin: auto;

        .step {
            position: relative;
            display: flex;
            align-items: center;
            gap: 1rem;
            padding:  0 2rem 2rem 3rem;
            border-left: .15rem solid var(--secondary-color);
            margin-left: 1rem;

            @media (max-width: 1100px) {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            &:last-child {
                border: none;
            }

            .left {
                display: flex;
                flex-direction: column;
                flex-grow: 2;
            }

            .number {
                position: absolute;
                top: 0;
                left: -1rem;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 2rem;
                height: 2rem;
                background: var(--secondary-color);
                color: #3a3a3a;
                font-weight: 500;
                border-radius: 50%;
            }

            .time {
                font-size: .875rem;
            }

            .name {
                font-size: 1.25rem;
                font-weight: 500;
            }

            .button {
                flex-shrink: 0;
            }
        }
    }

    .cta {
        display: flex;
        justify-content: center;
        margin-top: 4rem;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .festival-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin: 3rem 0;

        .feature {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 1rem;
            padding: 2rem;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            img {
                filter: invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
            }

            .title {
                font-weight: 600;
                font-size: 1.1rem;
                color: var(--primary-color);
            }
        }

        @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;

            .feature {
                padding: 1.5rem;
            }
        }
    }
 }