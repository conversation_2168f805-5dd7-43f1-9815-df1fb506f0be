<script setup>


const props = defineProps(['amount'])
const emit = defineEmits(['close'])

const date = new Date().toLocaleDateString('fr-FR', {day: 'numeric', month: 'long', year: 'numeric'});

function close() {
    emit('close');
}

</script>

<style lang="sass" scoped>
@use './ValidationScreenComponent.scss' as *
</style>

<template>
	<div class="screen-container" ref="container">
		<div class="check-container">
			<svg width="100" height="100" viewBox="0 0 100 100">
				<path class="check-mark" d="M20,50 L40,70 L80,30"/>
			</svg>
		</div>
		<div class="message"> Paiement Accepté </div>
		<div class="amount">{{ props.amount }},00€</div>
		<div class="date"> {{ date }} </div>
		<div class="actions">
			<div class="action" @click="close()"> Fermer </div>
		</div>
	</div>
</template>