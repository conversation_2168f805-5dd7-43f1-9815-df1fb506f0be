export class UserIdRepository {
    guidGenerator() {
        const S4 = function() {
            return (((1+Math.random())*0x10000)|0).toString(16).substring(1);
        };
        return (S4()+S4()+"-"+S4()+"-"+S4()+"-"+S4()+"-"+S4()+S4()+S4());
    }

    getUserId(){
        let userId = localStorage.getItem('userId');
        if(!userId) {
            userId = this.guidGenerator();
            localStorage.setItem('userId', userId);
        }
        return userId;
    }
}