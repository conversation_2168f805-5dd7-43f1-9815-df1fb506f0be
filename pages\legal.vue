<script setup>

import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";

</script>
>
<template>
    <div id="cookies-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="container">
            <div class="content">
                <div class="content-title">
                    <h1 class="heading-xl">{{ $t('legalPage.headingXL') }}</h1>
                </div>

                <h2>{{ $t('legalPage.heading2_1') }}</h2>
                <p>{{ $t('legalPage.paragraph1') }}</p>
                <p>{{ $t('legalPage.paragraph2') }}</p>
                <p>{{ $t('legalPage.paragraph3') }}</p>

                <h2>{{ $t('legalPage.heading2_2') }}</h2>
                <p>{{ $t('legalPage.paragraph4') }}</p>
                <p>{{ $t('legalPage.paragraph5') }}</p>
                <p>{{ $t('legalPage.paragraph6') }}</p>
                <p>{{ $t('legalPage.paragraph7') }}</p>
                <p>{{ $t('legalPage.paragraph8') }}</p>

                <h2>{{ $t('legalPage.heading2_3') }}</h2>
                <p>{{ $t('legalPage.paragraph9') }}</p>
                <p>{{ $t('legalPage.paragraph10') }}</p>
                <p>{{ $t('legalPage.paragraph11') }}</p>

                <h2>{{ $t('legalPage.heading2_4') }}</h2>
                <p>{{ $t('legalPage.paragraph12') }}</p>
                <p>{{ $t('legalPage.paragraph13') }}</p>
                <p>{{ $t('legalPage.paragraph14') }}</p>

                <h2>{{ $t('legalPage.heading2_5') }}</h2>
                <p>{{ $t('legalPage.paragraph15') }}</p>
                <p>{{ $t('legalPage.paragraph16') }}</p>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>

<style scoped lang="sass">

</style>