<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";
import TextImageContainerComponent from "~/components/TextImageContainerComponent/TextImageContainerComponent.vue";

const i18n = useI18n();

useHead({
    title: i18n.t('foodFestival.hero.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('foodFestival.hero.subtitle')
        },
        {
            property: 'og:title',
            content: i18n.t('foodFestival.hero.title')
        },
        {
            property: 'og:description',
            content: i18n.t('foodFestival.hero.subtitle')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ]
});
</script>

<style lang="sass">
@use '../pagesSrc/foodFestival/foodFestival.scss' as *
</style>

<template>
    <div id="food-festival-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="first container primary">
            <div class="content">
                <div class="hero-banner">
                    <div class="left">
                        <div>
                            <h1 class="title heading-2xl">{{ $t('foodFestival.hero.title') }}</h1>
                        </div>

                        <h2 class="subtitle heading-sm">{{ $t('foodFestival.hero.subtitle') }}</h2>

                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <div class="trusted-by-companies">
                            <span class="title">{{ $t('foodFestival.hero.trustedBy') }}</span>

                            <div class="companies">
                                <NuxtImg format="webp" alt="Fete des vendanges" src="/fete-des-vendanges.jpg" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="Aperos montois" src="/aperos-montois.png" sizes="xs:150px md:150px" />
                                <NuxtImg class="darken" format="webp" alt="TAC hockey" src="/festival-de-la-rillette.png" sizes="xs:150px md:150px" />
                            </div>
                        </div>
                    </div>

                    <div class="illustration">
                        <NuxtImg format="webp" alt="festival-illustration" width="581" height="502" :src="'/food-festival-' + $i18n.locale + '.png'" sizes="xs:420px md:600px" />
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="text-images">
                    <TextImageContainerComponent
                        :small="$t('foodFestival.features.simplification.small')"
                        :title="$t('foodFestival.features.simplification.title')"
                        :subtitle="$t('foodFestival.features.simplification.subtitle')"
                        heading-level="h2"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/interface-stats.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('foodFestival.features.fastExperience.small')"
                        :title="$t('foodFestival.features.fastExperience.title')"
                        :subtitle="$t('foodFestival.features.fastExperience.subtitle')"
                        :reversed="true"
                        heading-level="h2"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/food-queue.jpg" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('foodFestival.features.easyForExhibitors.small')"
                        :title="$t('foodFestival.features.easyForExhibitors.title')"
                        :subtitle="$t('foodFestival.features.easyForExhibitors.subtitle')"
                        heading-level="h2"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/sunmi/p2-lite-products.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>
                </div>
            </div>
        </div>

        <div class="container primary">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('foodFestival.clients.titleSmall') }}</span>
                    <h2 class="heading-xl">{{ $t('foodFestival.clients.title') }}</h2>
                </div>

                <div class="testimonials">
                    <div class="testimonial">
                        <div class="comment">{{ $t('foodFestival.clients.testimonial1.comment') }}</div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="testimonial1" width="36" height="36" src="/aperos-montois.png" sizes="xs:36px md:36px" />
                                <div class="data">
                                    <span class="name">{{ $t('foodFestival.clients.testimonial1.name') }}</span>
                                    <span class="company">{{ $t('foodFestival.clients.testimonial1.company') }}</span>
                                </div>
                            </div>
                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" v-for="i in 5" :key="i" />
                            </div>
                        </div>
                    </div>

                    <div class="testimonial">
                        <div class="comment">{{ $t('foodFestival.clients.testimonial2.comment') }}</div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="testimonial2" width="36" height="36" src="/fete-des-vendanges.jpg" sizes="xs:36px md:36px" />
                                <div class="data">
                                    <span class="name">{{ $t('foodFestival.clients.testimonial2.name') }}</span>
                                    <span class="company">{{ $t('foodFestival.clients.testimonial2.company') }}</span>
                                </div>
                            </div>
                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" v-for="i in 5" :key="i" />
                            </div>
                        </div>
                    </div>

                    <div class="testimonial">
                        <div class="comment">{{ $t('foodFestival.clients.testimonial3.comment') }}</div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="testimonial3" width="36" height="36" src="/festival-de-la-rillette.png" sizes="xs:36px md:36px" />
                                <div class="data">
                                    <span class="name">{{ $t('foodFestival.clients.testimonial3.name') }}</span>
                                    <span class="company">{{ $t('foodFestival.clients.testimonial3.company') }}</span>
                                </div>
                            </div>
                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" v-for="i in 5" :key="i" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-bubble">
                    <div class="content-title">
                        <h2 class="heading-xl">{{ $t('foodFestival.readyToSwitch.title') }}</h2>
                    </div>

                    <div class="big-buttons">
                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <NuxtLinkLocale to="/contact" class="grey button">
                            {{ $t('foodFestival.readyToSwitch.contact') }}
                        </NuxtLinkLocale>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>
