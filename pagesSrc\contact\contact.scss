#contact-page {
    >.primary {
        background: var(--soft-background-color);
    }

    .grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 4rem;
        padding: 6rem 0;

        @media (max-width: 1100px) {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            padding: 2rem 0;
        }
    }

    .illustration {
        img {
            height: auto;
            width: 100%;
        }
    }

    .form {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        textarea {
            height: 10rem;
        }

        label {
            font-size: 1rem;
            font-weight: 500;
        }
    }

    .contacts {
        display: flex;
        flex-direction: column;
        gap: 2rem;

        .contact-method {
            display: flex;
            flex-direction: column;
            gap: .25rem;

            .method-title {
                font-size: 1.5rem;
                font-weight: 500;
            }

            .contact {
                all: unset;
                font-size: 1rem;
                text-decoration: underline;
                cursor: pointer;
            }
        }
    }

    .sent {
        text-align: center;
        font-weight: 500;
    }
}