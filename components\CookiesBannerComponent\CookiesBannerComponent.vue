<script setup>

import { ref, onMounted } from 'vue';

const acceptedCookies = ref(localStorage.getItem('accepted-cookies'));

onMounted(() => {
    // Analytics initialization is now handled by the plugin
    // No need to inject gtag here anymore
});

function acceptCookies() {
    localStorage.setItem('accepted-cookies', 'true');
    acceptedCookies.value = 'true';

    // Use the new consent system from the plugin
    if (window.updateGoogleConsent) {
        window.updateGoogleConsent(true);
    } else {
        // Fallback if plugin hasn't loaded yet
        setTimeout(() => {
            if (window.updateGoogleConsent) {
                window.updateGoogleConsent(true);
            }
        }, 100);
    }

    sendToAirtable(true);
}

function refuseCookies() {
    localStorage.setItem('accepted-cookies', 'false');
    acceptedCookies.value = 'false';

    // Update consent to denied
    if (window.updateGoogleConsent) {
        window.updateGoogleConsent(false);
    }

    sendToAirtable(false);
}

// Remove the old injectGtag function - now handled by the plugin
// function injectGtag() {
//     // This is now handled by the plugin with proper timing
// }


async function sendToAirtable(accepted) {
    try {
        await fetch('https://pro.weecop.fr/cashl.es/cookies.php', {
            method: 'POST',
            body: JSON.stringify({
                'fields': {
                    'fldpNiAu0ffyw7i5E': accepted,
                    'fldHjEo8paXe14eGd': ''
                }
            })
        });
    } catch(err) {}
}

</script>

<style scoped lang="scss">
    .cookies-banner-component {
        position: fixed;
        bottom: 2rem;
        left: 2rem;
        display: flex;
        flex-direction: column;
        gap: .5rem;
        padding: 2rem;
        border-radius: 1rem;
        background: white;
        box-shadow: 0 0 12px rgba(0, 0, 0, 0.16);
        width: 20rem;

        @media (max-width: 1100px) {
            width: auto;
            left: 1rem;
            right: 1rem;
            bottom: 1rem;
        }

        .title {
            font-weight: 500;
            font-size: 1.125rem;
        }

        .buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: .5rem;

            .button {
                font-size: 1rem !important;
                padding: .4rem .6rem !important;
            }
        }
    }
</style>

<template>
    <div class="cookies-banner-component" v-if="acceptedCookies === null">
        <span class="title"> {{ $t('cookies.title') }}</span>
        <span class="description">
            {{ $t('cookies.description') }} <NuxtLinkLocale to="cookies"> {{ $t('cookies.policy') }} </NuxtLinkLocale>.
        </span>
        <div class="buttons">
            <button class="secondary button" @click="refuseCookies()">
                {{ $t('cookies.refuse') }}
            </button>
             <button class="primary button" @click="acceptCookies()">
                 {{ $t('cookies.accept') }}
            </button>
        </div>
    </div>
</template>
