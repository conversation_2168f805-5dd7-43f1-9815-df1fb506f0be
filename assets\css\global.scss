
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;1,400;1,500;1,600&display=swap');

//:root {
//    --primary-color: black;
//    --primary-text-color: white;
//    --secondary-color: #eaeaea;
//    --secondary-text-color: black;
//    --soft-background-color: #f7f7f8;
//}

:root {
    --primary-color: #1834FB;
    --primary-hover-color: #001cdd;
    --primary-text-color: white;
    --secondary-color: #e5e3df;
    --secondary-text-color: black;
    --soft-background-color: #FAF8F4;
}



html, body {
    margin: 0;
    padding: 0;
    font-family: Poppins, sans-serif;
}

html {
    overflow-y: scroll;
}

// Reset default heading styles to ensure consistent styling with our classes
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
    font-weight: inherit;
    font-size: inherit;
    line-height: inherit;
    letter-spacing: inherit;
}


.page .container {

    &.black {
        background: black;
        color: white;
    }

    .content {
        max-width: 1200px;
        box-sizing: border-box;
        padding: 0 1rem;
        margin: auto;
    }

    &:not(.first) {
        .content {
            padding: 4rem 1rem;
        }
    }

    .content-title {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 2.5rem;

        .small {
            font-size: 1rem;
            text-align: center;
            opacity: 0.8;
        }

        .heading-xl {
            text-align: center;
        }
    }

    .content-bubble {
        background: red;
        padding: 3rem;
        border-radius: 1rem;
        background: var(--soft-background-color);

        @media (max-width: 1100px) {
            padding: 3rem 1rem;
        }
    }

    .big-buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;

        @media (max-width: 1100px) {
            flex-direction: column;
        }

        .button {
            margin-top: 0.5rem;
            gap: 1rem;

            .hint {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 400;
                opacity: 0.8;
                font-size: .875rem;
            }

            img {
                height: 16px;
                filter: invert(100%) sepia(0%);
            }

            @media (max-width: 1100px) {
                font-size: .875rem;

                .hint {
                    font-size: .725rem;
                }
            }

            @media (max-width: 400px) {
                font-size: .725rem;

                .hint {
                    font-size: .725rem;
                }
            }
        }
    }
}

.button {
    all: unset;
    display: flex;
    align-items: center;
    gap: .5rem;
    padding: .75rem 1.25rem;
    border-radius: 4rem;
    font-size: 1rem;
    background: #D9D9D9;
    cursor: pointer;
    text-align: center;
    justify-content: center;

    &.primary {
        background: var(--primary-color);
        color: var(--primary-text-color);

        &:hover {
            background: var(--primary-hover-color);
        }
    }

    &.white {
        background: white;
        color: black;

        &:hover {
            background: #eaeaea;
        }
    }

    &.grey {
        background: var(--secondary-color);
        color: var(--secondary-text-color);
    }

    &.disabled {
        opacity: 0.5;
        pointer-events: none;
    }

    img {
        height: 16px;
    }
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: .2rem;

    label {
        opacity: 0.8;
    }

    input, textarea {
        all: unset;
        background: #ededed;
        border-radius: .5rem;
        padding: 0.5rem 1rem;
        box-sizing: border-box;
    }
}

.testimonials {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 2rem;
    align-items: end;

    @media (max-width: 1100px) {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        padding: 2rem 0;
    }

    .testimonial {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        background: white;
        padding: 2rem;
        border-radius: 1rem;

        .comment {

        }

        .bottom {
            display: flex;
            gap: .75rem;

            .client-infos {
                flex-grow: 2;
                display: flex;
                align-items: center;
                gap: .75rem;

                .image {
                    flex-shrink: 0;
                    height: 36px;
                    width: 36px;
                    background: red;
                    border-radius: 50%;
                }

                img {
                    flex-shrink: 0;
                    height: 36px;
                    width: 36px;
                    border-radius: 50%;
                }

                .data {
                    display: flex;
                    flex-direction: column;

                    .name {
                        font-size: .875rem;
                    }

                    .company  {
                        font-size: .875rem;
                        font-weight: 500;
                    }
                }
            }

            .stars {
                flex-grow: 0;
                display: flex;
                align-items: center;
                gap: .1rem;

                img {
                    height: 12px;
                    filter: invert(66%) sepia(57%) saturate(1580%) hue-rotate(9deg) brightness(103%) contrast(97%);
                }
            }
        }
    }
}

.heading-3xl {
    font-size: 2rem;
    line-height: 1.4;
    letter-spacing: -.047rem;
    font-weight: 500
}

@media (min-width: 768px) {
    .heading-3xl {
        font-size:2.625rem;
        line-height: 1.3
    }
}

@media (min-width: 992px) {
    .heading-3xl {
        font-size:3.25rem
    }
}

@media (min-width: 1200px) {
    .heading-3xl {
        font-size:4rem
    }
}

.heading-2xl {
    font-size: 1.875rem;
    line-height: 1.3;
    letter-spacing: -.031rem;
    font-weight: 500
}

@media (min-width: 768px) {
    .heading-2xl {
        font-size:2.25rem
    }
}

@media (min-width: 992px) {
    .heading-2xl {
        font-size:2.5rem;
        line-height: 1.4
    }
}

@media (min-width: 1200px) {
    .heading-2xl {
        font-size:3rem;
        line-height: 1.3
    }
}

.heading-xl {
    font-size: 1.75rem;
    line-height: 1.3;
    letter-spacing: -.016rem;
    font-weight: 500
}

@media (min-width: 768px) {
    .heading-xl {
        font-size:2rem;
        line-height: 1.4
    }
}

@media (min-width: 992px) {
    .heading-xl {
        font-size:2.25rem;
        line-height: 1.3
    }
}

@media (min-width: 1200px) {
    .heading-xl {
        font-size:2.5rem
    }
}

.heading-lg {
    font-size: 1.625rem;
    line-height: 1.4;
    letter-spacing: -.016rem;
    font-weight: 500
}

@media (min-width: 768px) {
    .heading-lg {
        font-size:1.75rem
    }
}

@media (min-width: 992px) {
    .heading-lg {
        font-size:1.875rem;
        line-height: 1.5
    }
}

@media (min-width: 1200px) {
    .heading-lg {
        font-size:2rem;
        line-height: 1.4
    }
}

.heading-md {
    font-size: 1.5rem;
    line-height: 1.5;
    font-weight: 500
}

@media (min-width: 768px) {
    .heading-md {
        font-size:1.625rem
    }
}

@media (min-width: 1200px) {
    .heading-md {
        font-size:1.75rem;
        line-height: 1.4
    }
}

.heading-sm {
    font-size: 1.25rem;
    line-height: 1.5;
    font-weight: 500
}

@media (min-width: 992px) {
    .heading-sm {
        font-size:1.375rem;
        line-height: 1.4
    }
}

.hero-banner {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 2rem;
    padding: 10rem 0;

    @media (max-width: 1100px) {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        padding: 2rem 0;
    }

    >.left {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 1.25rem;

        .title {
            font-weight: 500;

            &.bold {
                font-weight: 600;
            }
        }

        .subtitle {
            font-size: 1.375rem;
            font-weight: 400;
        }

        .button {
            margin-top: 0.5rem;
            gap: 1rem;

            .hint {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 400;
                opacity: 0.8;
                font-size: .875rem;
            }

            img {
                height: 16px;
                filter: invert(100%) sepia(0%);
            }

            @media (max-width: 1100px) {
                font-size: .875rem;

                .hint {
                    font-size: .725rem;
                }
            }
        }

        .trusted-by-companies {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
            width: 100%;
            margin-top: 20px;

            .title {
                font-size: 1rem;
                color: #3c3c3c;
                font-weight: 400 !important;
            }

            .companies {
                display: flex;
                justify-content: center;
                gap: 3rem;

                img {
                    height: 52px;

                    &.darken {
                        mix-blend-mode: darken;
                    }
                }
            }

            @media (max-width: 1100px) {
                .companies {
                    flex-wrap: wrap;
                }
            }
        }
    }
}

.desktop-only {
    @media (max-width: 1100px) {
        display: none;
    }
}
.mobile-only {
    @media (min-width: 1100px) {
        display: none;
    }
}