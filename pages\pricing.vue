<script setup>

import NavbarComponent from "../components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "../components/FooterComponent/FooterComponent.vue";

const i18n = useI18n();

useHead({
    title: i18n.t('pricing.seo.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('pricing.seo.description')
        },
        {
            property: 'og:title',
            content: i18n.t('pricing.seo.title')
        },
        {
            property: 'og:description',
            content: i18n.t('pricing.seo.description')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ],
    script: [
        {
            type: 'application/ld+json',
            children: {
                "@context": "https://schema.org",
                "@type": "FAQPage",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": i18n.t('faq.question3.title'),
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": i18n.t('faq.question3.answer'),
                        }
                    },
                    {
                        "@type": "Question",
                        "name": i18n.t('faq.question4.title'),
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": i18n.t('faq.question4.answer'),
                        }
                    },
                    {
                        "@type": "Question",
                        "name": i18n.t('pricing.faq.question3.title'),
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": i18n.t('pricing.faq.question3.answer'),
                        }
                    },
                    {
                        "@type": "Question",
                        "name": i18n.t('pricing.faq.question4.title'),
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": i18n.t('pricing.faq.question4.answer'),
                        }
                    }
                ]
            }
        }
    ]
});

let activeTab = ref('RFID');

let displayedQuestion = ref(null);

function toggleQuestion(questionId) {
    if (displayedQuestion.value === questionId) {
        displayedQuestion.value = null;
    } else {
        displayedQuestion.value = questionId;
    }
}

</script>

<style lang="sass" scoped>
@use '../pagesSrc/pricing/pricing.scss' as *
</style>
<template>
    <div id="pricing-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="first container primary">
            <div class="content">
                <div class="quote-cta">
                    <div class="content-title">
                        <span class="small">{{ $t('pricing.onlineQuote.small') }}</span>
                        <span class="heading-xl">{{ $t('pricing.onlineQuote.title') }}</span>
                    </div>

                    <NuxtLinkLocale to="/simulation" class="primary button">
                        {{ $t('home_hero.start_quote') }}
                    </NuxtLinkLocale>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('pricing.wristbandPricing.small') }}</span>
                    <span class="heading-xl">{{ $t('pricing.wristbandPricing.title') }}</span>
                </div>

                <div class="rfid-comparison">
                    <div class="column background">
                        <div class="item image-header background">
                            <NuxtImg format="webp" alt="Prix bracelet cashless festival personnalisable" src="/bracelet-festival-personnalisable.png" sizes="xs:420px md:600px" />
                        </div>
                        <span class="item name background">{{ $t('pricing.products.festival.name') }}</span>
                        <span class="item property background">{{ $t('pricing.products.festival.description') }}</span>
                    </div>

                    <div class="column background">
                        <div class="item image-header">
                            <NuxtImg format="webp" alt="Tarifs bracelet cashless resort réutilisable" src="/bracelet-resort.png" sizes="xs:420px md:600px" />
                        </div>
                        <span class="item name">{{ $t('pricing.products.resort.name') }}</span>
                        <span class="item property">{{ $t('pricing.products.resort.description') }}</span>
                    </div>

                    <div class="column background">
                        <div class="item image-header background">
                            <NuxtImg format="webp" loading="lazy" alt="Tarifs bracelet cashless resort réutilisable" src="/carte-cashless.png" sizes="xs:420px md:600px" />
                        </div>
                        <span class="item name background">{{ $t('pricing.products.pvcCard.name') }}</span>
                        <span class="item property background">{{ $t('pricing.products.pvcCard.description') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('pricing.details.small') }}</span>
                    <span class="heading-xl">{{ $t('pricing.details.title') }}</span>
                </div>

                <div class="pricing-details">
                    <div class="tabs">
                        <div class="tab" :class="{ active: activeTab === 'RFID' }" @click="activeTab = 'RFID'">
                            {{ $t('pricing.tabs.rfid') }}
                        </div>
                        <div class="tab" :class="{ active: activeTab === 'PAYMENT' }" @click="activeTab = 'PAYMENT'">
                            {{ $t('pricing.tabs.payment') }}
                        </div>
                    </div>

                    <!-- Restored RFID Tab Content -->
                    <template v-if="activeTab === 'RFID'">
                        <div id="pvc-card" class="pricing-detail">
                            <span class="heading-sm">{{ $t('pricing.rfid.pvcCard.title') }}</span>
                            <div class="table-overflow-container">
                                <table>
                                    <tbody>
                                    <tr class="desktop-only">
                                        <th>{{ $t('pricing.rfid.tableHeaders.quantity') }}</th>
                                        <th>{{ $t('pricing.rfid.tableHeaders.customPrice') }}</th>
                                        <th>{{ $t('pricing.rfid.tableHeaders.standardPrice') }}</th>
                                    </tr>
                                    <tr class="mobile-only">
                                        <th>{{ $t('pricing.rfid.tableHeaders.quantity') }}</th>
                                        <th> P* </th>
                                        <th> NP* </th>
                                    </tr>
                                    <tr>
                                        <td>500</td>
                                        <td>2,40€</td>
                                        <td>1,72€</td>
                                    </tr>
                                    <tr>
                                        <td>1 000</td>
                                        <td>1,72€</td>
                                        <td>1,50€</td>
                                    </tr>
                                    <tr>
                                        <td>5 000</td>
                                        <td>1,50€</td>
                                        <td>1,20€</td>
                                    </tr>
                                    <tr>
                                        <td>10 000</td>
                                        <td>1,20€</td>
                                        <td>1,00€</td>
                                    </tr>
                                    <tr>
                                        <td>25 000</td>
                                        <td>1,00€</td>
                                        <td>0,89€</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mobile-legend">
                                <span> P*: {{ $t('pricing.rfid.tableHeaders.customPrice') }} </span>
                                <span> NP*: {{ $t('pricing.rfid.tableHeaders.standardPrice') }} </span>
                            </div>
                        </div>

                        <div id="festival-wristband" class="pricing-detail">
                            <span class="heading-sm">{{ $t('pricing.rfid.festivalWristband.title') }}</span>
                            <div class="table-overflow-container">
                                <table>
                                    <tbody>
                                    <tr class="desktop-only">
                                        <th>{{ $t('pricing.rfid.tableHeaders.quantity') }}</th>
                                        <th>{{ $t('pricing.rfid.tableHeaders.customPrice') }}</th>
                                        <th>{{ $t('pricing.rfid.tableHeaders.standardPrice') }}</th>
                                    </tr>
                                    <tr class="mobile-only">
                                        <th>{{ $t('pricing.rfid.tableHeaders.quantity') }}</th>
                                        <th> P* </th>
                                        <th> NP* </th>
                                    </tr>
                                    <tr>
                                        <td>500</td>
                                        <td>2,40€</td>
                                        <td>1,72€</td>
                                    </tr>
                                    <tr>
                                        <td>1 000</td>
                                        <td>1,72€</td>
                                        <td>1,50€</td>
                                    </tr>
                                    <tr>
                                        <td>5 000</td>
                                        <td>1,50€</td>
                                        <td>1,20€</td>
                                    </tr>
                                    <tr>
                                        <td>10 000</td>
                                        <td>1,20€</td>
                                        <td>1,00€</td>
                                    </tr>
                                    <tr>
                                        <td>25 000</td>
                                        <td>1,00€</td>
                                        <td>0,89€</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mobile-legend">
                                <span> P*: {{ $t('pricing.rfid.tableHeaders.customPrice') }} </span>
                                <span> NP*: {{ $t('pricing.rfid.tableHeaders.standardPrice') }} </span>
                            </div>
                        </div>

                        <div id="resort-wristband" class="pricing-detail">
                            <span class="heading-sm">{{ $t('pricing.rfid.resortWristband.title') }}</span>
                            <div class="table-overflow-container">
                                <table>
                                    <tbody>
                                    <tr class="desktop-only">
                                        <th>{{ $t('pricing.rfid.tableHeaders.quantity') }}</th>
                                        <th>{{ $t('pricing.rfid.tableHeaders.customPrice') }}</th>
                                        <th>{{ $t('pricing.rfid.tableHeaders.standardPrice') }}</th>
                                    </tr>
                                    <tr class="mobile-only">
                                        <th>{{ $t('pricing.rfid.tableHeaders.quantity') }}</th>
                                        <th> P* </th>
                                        <th> NP* </th>
                                    </tr>
                                    <tr>
                                        <td>500</td>
                                        <td>2,40€</td>
                                        <td>1,72€</td>
                                    </tr>
                                    <tr>
                                        <td>1 000</td>
                                        <td>1,72€</td>
                                        <td>1,50€</td>
                                    </tr>
                                    <tr>
                                        <td>5 000</td>
                                        <td>1,50€</td>
                                        <td>1,20€</td>
                                    </tr>
                                    <tr>
                                        <td>10 000</td>
                                        <td>1,20€</td>
                                        <td>1,00€</td>
                                    </tr>
                                    <tr>
                                        <td>25 000</td>
                                        <td>1,00€</td>
                                        <td>0,89€</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mobile-legend">
                                <span> P*: {{ $t('pricing.rfid.tableHeaders.customPrice') }} </span>
                                <span> NP*: {{ $t('pricing.rfid.tableHeaders.standardPrice') }} </span>
                            </div>
                        </div>
                    </template>

                    <!-- Payment Tab Content -->
                    <template v-else>
                        <div class="pricing-detail">
                            <div class="table-overflow-container">
                                <table>
                                    <tbody>
                                    <tr>
                                        <th>{{ $t('pricing.payment.tableHeaders.perDay') }}</th>
                                        <th>{{ $t('pricing.payment.tableHeaders.perMonth') }}</th>
                                        <th>{{ $t('pricing.payment.tableHeaders.perYear') }}</th>
                                        <th>{{ $t('pricing.payment.tableHeaders.purchase') }}</th>
                                    </tr>
                                    <tr>
                                        <td>
                                            19,99€ / {{ $t('pricing.payment.day') }}<br />
                                            <br />
                                            >50 {{ $t('pricing.payment.terminals') }}<br />
                                            9,99€ / {{ $t('pricing.payment.day') }}<br />
                                            <br />
                                            {{ $t('pricing.payment.weekendPack') }}: 29,99€<br />
                                            {{ $t('pricing.payment.deliveryFrom') }} {{ $t('common.days.friday') }}.<br />
                                            <br />
                                            {{ $t('pricing.payment.weekPack') }}: 63,93€
                                        </td>
                                        <td>99,99€ / {{ $t('pricing.payment.month') }}</td>
                                        <td>49,99€ / {{ $t('pricing.payment.month') }}</td>
                                        <td>{{ $t('pricing.payment.from') }} 499€</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('pricing.faq.small') }}</span>
                    <span class="heading-xl">{{ $t('pricing.faq.title') }}</span>
                </div>

                <div class="faq">
                    <div class="group" @click="toggleQuestion('1')">
                        <div class="question">
                            {{ $t('faq.question3.title') }}
                            <img
                                :class="{ rotated: displayedQuestion === '1' }"
                                alt="chevron"
                                src="~/assets/img/chevron-down.svg"
                            />
                        </div>
                        <div class="response" :class="{ showed: displayedQuestion === '1' }">
                            {{ $t('faq.question3.answer') }}
                        </div>
                    </div>

                    <div class="group" @click="toggleQuestion('2')">
                        <div class="question">
                            {{ $t('faq.question4.title') }}
                            <img
                                :class="{ rotated: displayedQuestion === '2' }"
                                alt="chevron"
                                src="~/assets/img/chevron-down.svg"
                            />
                        </div>
                        <div class="response" :class="{ showed: displayedQuestion === '2' }">
                            {{ $t('faq.question4.answer') }}
                        </div>
                    </div>

                    <div class="group" @click="toggleQuestion('3')">
                        <div class="question">
                            {{ $t('pricing.faq.question3.title') }}
                            <img
                                :class="{ rotated: displayedQuestion === '3' }"
                                alt="chevron"
                                src="~/assets/img/chevron-down.svg"
                            />
                        </div>
                        <div class="response" :class="{ showed: displayedQuestion === '3' }">
                            {{ $t('pricing.faq.question3.answer') }}
                        </div>
                    </div>

                    <div class="group" @click="toggleQuestion('4')">
                        <div class="question">
                            {{ $t('pricing.faq.question4.title') }}
                            <img
                                :class="{ rotated: displayedQuestion === '4' }"
                                alt="chevron"
                                src="~/assets/img/chevron-down.svg"
                            />
                        </div>
                        <div class="response" :class="{ showed: displayedQuestion === '4' }">
                            {{ $t('pricing.faq.question4.answer') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>
