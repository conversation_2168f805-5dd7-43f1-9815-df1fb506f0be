image: docker:stable

variables:
  ARTIFACTS_IMAGES_DIRECTORY: "artifacts_images"
  IMAGE_NAME_FINAL: "${CI_REGISTRY_IMAGE}/nginx"
  IMAGE_NAME_FINAL_ARTIFACT: "${ARTIFACTS_IMAGES_DIRECTORY}/docker-image.tar"
  IMAGE_NAME_TESTS: "cashles_devcontainer"
  IMAGE_NAME_TESTS_ARTIFACT: "${ARTIFACTS_IMAGES_DIRECTORY}/docker-image_tests.tar"
  DOCKER_BUILDKIT: 1

services:
  - docker:dind

stages:
  - build

build:
  stage: build
  interruptible: true
  script:
    - CI_COMMIT_BRANCH_DOCKER=${CI_COMMIT_BRANCH//\//\-}
    - export CI_COMMIT_BRANCH_DOCKER
    - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh
    - chmod +x docker/********************.sh && ./docker/********************.sh

