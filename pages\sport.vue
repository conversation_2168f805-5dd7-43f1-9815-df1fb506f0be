<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";
import TextImageContainerComponent from "~/components/TextImageContainerComponent/TextImageContainerComponent.vue";

const i18n = useI18n();

useHead({
    title: i18n.t('sport.seo.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('sport.seo.description')
        },
        {
            property: 'og:title',
            content: i18n.t('sport.seo.title')
        },
        {
            property: 'og:description',
            content: i18n.t('sport.seo.description')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ]
});

</script>

<style lang="sass">
@use '../pagesSrc/sport/sport.scss' as *
</style>

<template>
    <div id="sport-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="first container primary">
            <div class="content">
                <div class="hero-banner">
                    <div class="left">
                        <div>
                            <span class="title heading-2xl">{{ $t('sport.hero.title') }}</span>
                        </div>

                        <span class="subtitle heading-sm">{{ $t('sport.hero.subtitle') }}</span>

                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <div class="trusted-by-companies">
                            <span class="title">{{ $t('sport.hero.trustedBy') }}</span>

                            <div class="companies">
                                <NuxtImg format="webp" alt="UTBM" src="/utbm.png" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="UST" src="/ust.png" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="TAC hockey" src="/tac-hockey.jpg" sizes="xs:150px md:150px" />
                            </div>
                        </div>
                    </div>

                    <div class="illustration">
                        <NuxtImg format="webp" alt="sport-illustration" width="609" height="445" :src="'/sport-' + $i18n.locale + '.png'" sizes="xs:420px md:600px" />
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="text-images">
                    <TextImageContainerComponent
                        :small="$t('sport.features.boostRevenue.small')"
                        :title="$t('sport.features.boostRevenue.title')"
                        :subtitle="$t('sport.features.boostRevenue.subtitle')"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="1534" height="1055" src="/beer-pressure.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('sport.features.fastPayments.small')"
                        :title="$t('sport.features.fastPayments.title')"
                        :subtitle="$t('sport.features.fastPayments.subtitle')"
                        :reversed="true"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="5076" height="3384" src="/food-queue.jpg" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('sport.features.transparency.small')"
                        :title="$t('sport.features.transparency.title')"
                        :subtitle="$t('sport.features.transparency.subtitle')"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/interface-stats.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>
                </div>
            </div>
        </div>

        <div class="container primary">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('sport.testimonials.small') }}</span>
                    <span class="heading-xl">{{ $t('sport.testimonials.title') }}</span>
                </div>

                <div class="testimonials">
                    <div class="testimonial">
                        <div class="comment">
                            {{ $t('sport.testimonials.comment1') }}
                        </div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="TAC Hockey" width="36" height="36" :src="'/tac-hockey.jpg'" sizes="xs:36px md:36px" />

                                <div class="data">
                                    <span class="name">Bernard Coisne</span>
                                    <span class="company">TAC Hockey</span>
                                </div>
                            </div>

                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                <img src="~/assets/img/star.svg" alt="star" class="icon" />
                            </div>
                        </div>
                    </div>
<!--                    <div class="testimonial">-->
<!--                        <div class="comment">-->
<!--                            {{ $t('sport.testimonials.comment2') }}-->
<!--                        </div>-->
<!--                        <div class="bottom">-->
<!--                            <div class="client-infos">-->
<!--                                <div class="image"></div>-->

<!--                                <div class="data">-->
<!--                                    <span class="name">Lucas Lelaidier</span>-->
<!--                                    <span class="company">Cashl.es</span>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                            <div class="stars">-->
<!--                                <img src="~/assets/img/star.svg" alt="star" class="icon" />-->
<!--                                <img src="~/assets/img/star.svg" alt="star" class="icon" />-->
<!--                                <img src="~/assets/img/star.svg" alt="star" class="icon" />-->
<!--                                <img src="~/assets/img/star.svg" alt="star" class="icon" />-->
<!--                                <img src="~/assets/img/star.svg" alt="star" class="icon" />-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-bubble">
                    <div class="content-title">
                        <span class="heading-xl">{{ $t('sport.cta.title') }}</span>
                    </div>

                    <div class="big-buttons">
                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <NuxtLinkLocale to="/contact" class="grey button">
                            {{ $t('sport.cta.contactUs') }}
                        </NuxtLinkLocale>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>
