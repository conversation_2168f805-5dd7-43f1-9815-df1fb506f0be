.navbar-container {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: var(--soft-background-color);
    transition: background-color .2s ease-in-out;

    &.filled {
        background: white;
    }
}

.navbar-component {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    padding: 1.25rem 1rem;
    max-width: 1200px;
    margin: auto;
    box-sizing: border-box;

    a {
        all: unset;
        cursor: pointer;
    }


    .logo {
        flex: 0;
        font-weight: bold;
        font-size: 1.25rem;
    }

    .center {
        flex-grow: 2;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: .75rem;
        font-size: .875rem;
        font-weight: 500;

        @media (max-width: 1100px) {
            display: none;
        }

        .item {
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
            gap: .3rem;
            padding: .5rem 1rem;
            border-radius: 10rem;

            .hoverable-container {
                position: absolute;
                display: none;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                padding-top: .5rem;

                .hoverable {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    padding: .5em;
                    border-radius: .75rem;
                    background-color: white;
                    box-shadow: 0 0 12px rgba(48, 48, 48, 0.12);
                    min-width: 30rem;

                    .example {
                        display: flex;
                        align-items: center;
                        gap: .75rem;
                        padding: .75rem;
                        border-radius: .75rem;

                        &:hover {
                            background-color: #f9f9f9;
                        }

                        img {
                            height: 32px;
                            width: 32px;
                            border-radius: .5rem;
                            flex-shrink: 0;
                        }
                    }
                }
            }


            img {
                height: 12px;
            }

            &:hover {
                background: var(--secondary-color);

                .hoverable-container {
                    display: initial;
                }
            }
        }
    }

    .right {
        display: flex;
        align-items: center;
        gap: .75rem;

        .item {
            display: flex;
            align-items: center;
            cursor: pointer;
            gap: .3rem;
            padding: .5rem 1rem;
            border-radius: 10rem;
            background: var(--secondary-color);
            font-size: .875rem;
            font-weight: 500;

            img {
                height: 12px;
            }
        }

        @media (max-width: 1100px) {
            .contact-button {
                display: none;
            }
        }

        .locale-changer {
            position: relative;
            display: flex;
            align-items: center;
            gap: .3rem;
            border-radius: 10rem;
            cursor: pointer;
            font-size: .875rem;
            font-weight: 400;

            select {
                all: unset;
                text-align: center;
                inset: 0;
                padding: .5rem 1rem .5rem 2rem;
            }

            img {
                position: absolute;
                height: 12px;
                left: .75rem;
            }

            &:hover {
                background: #eaeaea;
            }
        }

        .menu-nav {
            display: flex;
            align-items: center;

            @media (min-width: 1100px) {
                display: none;
            }

            img {
                height: 24px;
            }
        }
    }
}

.mobile-panel {
    position: fixed;
    inset: 0;
    background: white;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1);

    &.opened {
        transform: none;
    }

    a {
        all: unset;
        cursor: pointer;
    }

    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem 1rem;

        .logo {
            flex: 0;
            font-weight: bold;
            font-size: 1.25rem;
        }

        img {
            height: 24px;
        }
    }

    .links {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 1.25rem 1rem;

        .item {
            display: flex;
            align-items: center;
            cursor: pointer;
            gap: .3rem;
            border-radius: 10rem;
            font-size: 1rem;
            font-weight: 500;

            img {
                height: 12px;
            }
        }
    }

    .examples {
        display: grid;
        grid-template-columns: 1fr 1fr;
        border-radius: .75rem;

        @media (max-width: 1100px) {
            grid-template-columns: 1fr;
        }

        .example {
            display: flex;
            align-items: center;
            gap: .75rem;
            padding: .75rem;
            border-radius: .75rem;

            &:hover {
                background-color: #f9f9f9;
            }

            img {
                height: 32px;
                width: 32px;
                border-radius: .5rem;
                flex-shrink: 0;
            }
        }
    }
}