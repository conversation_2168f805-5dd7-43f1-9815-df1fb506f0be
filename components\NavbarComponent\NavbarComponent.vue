<script setup>

const router = useRouter()
const switchLocalePath = useSwitchLocalePath()
const { locale, locales } = useI18n()
let scrolled = ref(false);
let opened = ref(false);
let openedSolutions = ref(true);
let selectedValue = ref(locale.value);

const updateScrollState = () => {
    scrolled.value = window.scrollY !== 0;
};

onMounted(() => {
    updateScrollState(); // Check initial scroll state
    window.addEventListener("scroll", updateScrollState);
});

onUnmounted(() => {
    window.removeEventListener("scroll", updateScrollState);
});

function onChange(event) {
    router.replace(switchLocalePath(event));
}

</script>

<style lang="sass" scoped>
@use './NavbarComponent.scss' as *
</style>

<template>
    <div class="navbar-container" :class="{filled: scrolled}">
        <div class="navbar-component">
            <NuxtLinkLocale to="index">
                <div class="logo">
                    Cashl.es
                </div>
            </NuxtLinkLocale>

            <div class="center">
                <NuxtLinkLocale to="hardware" class="item"> {{ $t('navbar.hardware') }} </NuxtLinkLocale>
                <div class="item">
                    {{ $t('navbar.solutions') }}
                    <img alt="chevron" src="~/assets/img/chevron-down.svg" />

                    <div class="hoverable-container">
                        <div class="hoverable">
                            <NuxtLinkLocale to="festival" class="example">
                                <img src="../../assets/img/music.svg" alt="music festival">
                                <span class="name"> {{ $t('navbar.examples.music_festival') }} </span>
                            </NuxtLinkLocale>
                            <NuxtLinkLocale to="food-festival" class="example">
                                <img src="../../assets/img/food-festival.svg" alt="food festival">
                                <span class="name"> {{ $t('navbar.examples.food_festival') }} </span>
                            </NuxtLinkLocale>
                            <NuxtLinkLocale to="gala" class="example">
                                <img src="../../assets/img/gala.svg" alt="gala">
                                <span class="name"> {{ $t('navbar.examples.gala') }} </span>
                            </NuxtLinkLocale>
                            <NuxtLinkLocale to="sport" class="example">
                                <img src="../../assets/img/tennis.svg" alt="sporting events">
                                <span class="name"> {{ $t('navbar.examples.sport') }} </span>
                            </NuxtLinkLocale>
                        </div>
                    </div>
                </div>
                <NuxtLinkLocale to="pricing" class="item"> {{ $t('navbar.pricing') }} </NuxtLinkLocale>
<!--                <div class="item"> {{ $t('navbar.resources') }} </div>-->
            </div>

            <div class="right">
                <NuxtLinkLocale to="contact" class="contact-button item">
                    {{ $t('navbar.contactUs') }}
                    <img alt="chevron" src="~/assets/img/chevron-right.svg" />
                </NuxtLinkLocale>

                <label for="lang-dropdown" class="locale-changer">
                    <img alt="globe" src="../../assets/img/globe.svg" />

                    <select id="lang-dropdown" v-model="selectedValue" @change="onChange(selectedValue)">
                        <option
                            v-for="(locale, index) in $i18n.locales"
                            :key="index"
                            :value="locale.code"
                        >{{locale.code}}</option>
                    </select>
                </label>

                <div class="menu-nav" @click="opened = true">
                    <img alt="menu" src="~/assets/img/bars.svg" />
                </div>
            </div>
        </div>

        <div class="mobile-panel" :class="{opened: opened}">
            <div class="top">
                <NuxtLinkLocale to="index">
                    <div class="logo">
                        Cashl.es
                    </div>
                </NuxtLinkLocale>

                <div class="menu-nav" @click="opened = false">
                    <img alt="xmark" src="~/assets/img/xmark.svg" />
                </div>
            </div>

            <div class="links">
                <NuxtLinkLocale to="hardware" class="item">
                    {{ $t('navbar.hardware') }}
                    <img alt="chevron" src="~/assets/img/chevron-right.svg" />
                </NuxtLinkLocale>

                <div class="item">
                    {{ $t('navbar.solutions') }}
                    <img alt="chevron" src="~/assets/img/chevron-down.svg" v-if="!openedSolutions" />
                </div>

                <div class="examples" v-if="openedSolutions">
                    <NuxtLinkLocale to="festival" class="example">
                        <img src="../../assets/img/music.svg" alt="music festival">
                        <span class="name"> {{ $t('navbar.examples.music_festival') }} </span>
                    </NuxtLinkLocale>
                    <NuxtLinkLocale to="food-festival" class="example">
                        <img src="../../assets/img/food-festival.svg" alt="food festival">
                        <span class="name"> {{ $t('navbar.examples.food_festival') }} </span>
                    </NuxtLinkLocale>
                    <NuxtLinkLocale to="gala" class="example">
                        <img src="../../assets/img/gala.svg" alt="student gala">
                        <span class="name"> {{ $t('navbar.examples.gala') }} </span>
                    </NuxtLinkLocale>
                    <NuxtLinkLocale to="sport" class="example">
                        <img src="../../assets/img/tennis.svg" alt="sporting events">
                        <span class="name"> {{ $t('navbar.examples.sport') }} </span>
                    </NuxtLinkLocale>
                </div>

                <NuxtLinkLocale to="pricing" class="item">
                    {{ $t('navbar.pricing') }}
                    <img alt="chevron" src="~/assets/img/chevron-right.svg" />
                </NuxtLinkLocale>

<!--                <div class="item">-->
<!--                    {{ $t('navbar.resources') }}-->
<!--                    <img alt="chevron" src="~/assets/img/chevron-right.svg" />-->
<!--                </div>-->

                <NuxtLinkLocale to="contact" class="contact-button item">
                    {{ $t('navbar.contactUs') }}
                    <img alt="chevron" src="~/assets/img/chevron-right.svg" />
                </NuxtLinkLocale>
            </div>
        </div>
    </div>
</template>