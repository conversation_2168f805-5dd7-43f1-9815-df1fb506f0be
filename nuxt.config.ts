// https://nuxt.com/docs/api/configuration/nuxt-config
// import vueFacingDecoratorIntellij from "@groupk/vite-vfc-intellij";

export default defineNuxtConfig({
    devServer: {
      port: 3005,
      host: "0.0.0.0",
    },
    compatibilityDate: '2024-11-01',
    devtools: {enabled: true},
    modules: ['@nuxt/image', '@nuxtjs/i18n', '@nuxtjs/sitemap'],
    site: {
        url: 'https://cashl.es',
        name: 'Cashl.es'
    },
    app: {
        head: {
            meta: [
                {content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'}
            ],
            link: [
                // { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
                // { rel: 'preconnect', href: 'https://https://fonts.gstatic.com', crossorigin: '' },
                // { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;1,400;1,500;1,600&display=swap' },
                { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
                { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
                { rel: 'icon', type: 'image/png', sizes: '48x48', href: '/favicon.png' },
                { rel: 'icon', type: 'image/png', sizes: '64x64', href: '/favicon.png' },
                { rel: 'icon', type: 'image/png', sizes: '152x152', href: '/favicon.png' },
                { rel: 'icon', type: 'image/png', sizes: '180x180', href: '/favicon.png' },
                { rel: 'icon', type: 'image/png', sizes: '192x192', href: '/favicon.png' },
            ]
        }
    },
    i18n: {
        vueI18n: './i18n.config.ts',
        strategy: 'prefix',
        baseUrl: 'https://cashl.es',
        locales: [
            {code: 'en', language: 'en-US', name: 'English'},
            {code: 'fr', language: 'fr-FR', name: 'Français'},
            {code: 'de', language: 'de-DE', name: 'Deutch'},
            {code: 'it', language: 'it-IT', name: 'Italian'},
        ],
        rootRedirect: {
          statusCode: 301,
          path: '',
        },
        customRoutes: 'config',
    },
    runtimeConfig: {
        public: {
            gtagId: 'G-DRRYKC1G2M',
        }
    },
    ssr: true,
    vite: {
        esbuild: {
            tsconfigRaw: {
                compilerOptions: {
                    experimentalDecorators: true,
                },
            },
        },

        build: {
            rollupOptions: {
                plugins: [
                    require('@rollup/plugin-html')(),
                ],
            },
        },
    },
})