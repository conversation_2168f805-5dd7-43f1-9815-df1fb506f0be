<script setup>

import {NavbarComponent, FooterComponent, CardContentComponent, TextImageContainerComponent} from "#components";
import SimpleSmartphoneComponent from "~/components/SimpleSmartphoneComponent/SimpleSmartphoneComponent.vue";

const i18n = useI18n();

useHead({
    title: i18n.t('home_hero.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('home_hero.meta_description')
        },
        {
            property: 'og:title',
            content: i18n.t('home_hero.title')
        },
        {
            property: 'og:description',
            content: i18n.t('home_hero.meta_description')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ],
    script: [
        {
            type: 'application/ld+json',
            children: {
                "@context": "https://schema.org",
                "@type": "FAQPage",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": i18n.t('faq.question1.title'),
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": i18n.t('faq.question1.answer'),
                        }
                    },
                    {
                        "@type": "Question",
                        "name": i18n.t('faq.question2.title'),
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": i18n.t('faq.question2.answer'),
                        }
                    },
                    {
                        "@type": "Question",
                        "name": i18n.t('faq.question3.title'),
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": i18n.t('faq.question3.answer'),
                        }
                    }
                ]
            }
        }
    ]
});

function toggleQuestion(id) {
    if(displayedQuestion.value === id) displayedQuestion.value = null;
    else displayedQuestion.value = id;
}

let displayedQuestion = ref(null);
let data = ref(0);

</script>

<style lang="sass">
@use '../pagesSrc/cashless/cashless.scss' as *
</style>

<template>
    <div id="homepage" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="first container primary">
            <div class="content">
                <div class="hero-banner" @click="data++">
                    <div class="left">
                        <div>
                            <h1 class="title heading-2xl">{{ $t('home_hero.title') }}</h1>
                        </div>

                        <h2 class="subtitle heading-sm">{{ $t('home_hero.subtitle') }}</h2>

                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock"/>
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <div class="social">
                            <div class="clients">
                                <NuxtImg format="webp" alt="Howard Hinton" src="/howard-hinton.jpeg" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="Aperos montois" src="/barracuda.jpg" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="INSA" src="/insa.png" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="Polytech" src="/polytech.png" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="Societe generale" src="/societe-generale.svg" sizes="xs:150px md:150px" />
                            </div>

                            <div class="right">
                                <div class="stars">
                                    <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                    <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                    <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                    <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                    <img src="~/assets/img/star.svg" alt="star" class="icon" />
                                </div>
                                <span> {{ $t('home_hero.trusted_by') }} </span>
                            </div>
                        </div>
                    </div>

                    <div class="illustration">
<!--                        <NuxtImg alt="customizable-rfid-chips" width="520" height="418" src="/hero-image.svg" sizes="xs:420px md:600px"/>-->
<!--                        <NuxtImg alt="customizable-rfid-chips" width="1137" height="2034" src="/sunmi/p2-Lite-SE-squished.png" sizes="xs:420px md:600px"/>-->

                        <SimpleSmartphoneComponent></SimpleSmartphoneComponent>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('cashless.why.small') }}</span>
                    <h2 class="heading-xl">{{ $t('cashless.why.title') }}</h2>
                </div>

                <div class="cards">
                    <CardContentComponent
                        :title="$t('cashless.advantages.centralize.title')"
                        :description="$t('cashless.advantages.centralize.description')"
                        :horizontal="true"
                        heading-level="h3"
                    >
                        <NuxtImg alt="Moins d'espèces = Moins de problèmes" format="webp" width="1297" height="886" src="/no-cash.png" sizes="xs:420px md:600px" loading="lazy"/>
                    </CardContentComponent>

                    <div class="two-cards">
                        <CardContentComponent
                            :title="$t('cashless.advantages.reliable.title')"
                            :description="$t('cashless.advantages.reliable.description')"
                            heading-level="h3"
                        >
                            <NuxtImg alt="Le Cashless même sans internet" format="webp" width="1575" height="886" src="/no-internet.png" sizes="xs:420px md:420px" loading="lazy"/>
                        </CardContentComponent>

                        <CardContentComponent
                            :title="$t('cashless.advantages.customizable.title')"
                            :description="$t('cashless.advantages.customizable.description')"
                            heading-level="h3"
                        >
                            <NuxtImg alt="Personalisation des cartes Cashless" format="webp" width="1846" height="1020" src="/variations.png" sizes="xs:420px md:600px" loading="lazy"/>
                        </CardContentComponent>
                    </div>
                </div>
            </div>
        </div>

        <div class="container black">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('cashless.why_us.small') }}</span>
                    <h2 class="heading-xl">{{ $t('cashless.why_us.title') }}</h2>
                </div>

                <div class="proof-grid">
                    <div class="proof">
                        <img src="~/assets/img/gear.svg" alt="gear" width="32" height="32"/>
                        <h3 class="title">{{ $t('cashless.proofs.easy.title') }}</h3>
                        <div class="description">{{ $t('cashless.proofs.easy.description') }}</div>
                    </div>

                    <div class="proof">
                        <img src="~/assets/img/money-bill.svg" alt="money" width="32" height="32"/>
                        <h3 class="title">{{ $t('cashless.proofs.transparent.title') }}</h3>
                        <div class="description">{{ $t('cashless.proofs.transparent.description') }}</div>
                    </div>

                    <div class="proof">
                        <img src="~/assets/img/shield.svg" alt="security" width="32" height="32"/>
                        <h3 class="title">{{ $t('cashless.proofs.secure.title') }}</h3>
                        <div class="description">{{ $t('cashless.proofs.secure.description') }}</div>
                    </div>

                    <div class="proof">
                        <img src="~/assets/img/star-of-life.svg" alt="adaptability" width="32" height="32"/>
                        <h3 class="title">{{ $t('cashless.proofs.adaptable.title') }}</h3>
                        <div class="description">{{ $t('cashless.proofs.adaptable.description') }}</div>
                    </div>

                    <div class="proof">
                        <img src="~/assets/img/chart-pie-simple.svg" alt="analytics" width="32" height="32"/>
                        <h3 class="title">{{ $t('cashless.proofs.analytics.title') }}</h3>
                        <div class="description">{{ $t('cashless.proofs.analytics.description') }}</div>
                    </div>

                    <div class="proof">
                        <img src="~/assets/img/hourglass.svg" alt="ux" width="32" height="32"/>
                        <h3 class="title">{{ $t('cashless.proofs.ux.title') }}</h3>
                        <div class="description">{{ $t('cashless.proofs.ux.description') }}</div>
                    </div>
                </div>

                <div class="cta">
                    <NuxtLinkLocale to="simulation" class="primary button">
                        {{ $t('cashless.cta.quote') }}
                    </NuxtLinkLocale>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="text-images">
                    <TextImageContainerComponent
                        :small="$t('home_section1.small')"
                        :title="$t('home_section1.title')"
                        :subtitle="$t('home_section1.subtitle')"
                        heading-level="h2"
                    >
                        <NuxtImg
                            alt="placeholder"
                            format="webp"
                            width="600"
                            height="450"
                            src="/foodtrucks.png"
                            sizes="xs:420px md:600px"
                            loading="lazy"
                        />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('home_section2.small')"
                        :title="$t('home_section2.title')"
                        :subtitle="$t('home_section2.subtitle')"
                        :reversed="true"
                        heading-level="h2"
                    >
                        <NuxtImg
                            alt="placeholder"
                            format="webp"
                            width="4096"
                            height="2731"
                            src="/festival-fast.png"
                            sizes="xs:420px md:600px"
                            loading="lazy"
                        />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('home_section3.small')"
                        :title="$t('home_section3.title')"
                        :subtitle="$t('home_section3.subtitle')"
                        heading-level="h2"
                    >
                        <NuxtImg
                            alt="placeholder"
                            format="webp"
                            width="600"
                            height="450"
                            src="/cashless-data.png"
                            sizes="xs:420px md:600px"
                            loading="lazy"
                        />
                    </TextImageContainerComponent>
                </div>
            </div>
        </div>

        <!-- Section 2 : Etapes -->
        <div class="container primary">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('steps_section.small') }}</span>
                    <h2 class="heading-xl">{{ $t('steps_section.title') }}</h2>
                </div>

                <div class="lifeline">
                    <!-- Étape 1 -->
                    <div class="step">
                        <div class="number">1</div>
                        <div class="left">
                            <span class="time">{{ $t('steps.step1.time') }}</span>
                            <h3 class="name">{{ $t('steps.step1.name') }}</h3>
                            <span class="description">{{ $t('steps.step1.description') }}</span>
                        </div>
                        <NuxtLinkLocale to="simulation" class="primary button">{{ $t('steps.step1.button') }}</NuxtLinkLocale>
                    </div>

                    <!-- Étape 2 -->
                    <div class="step">
                        <div class="number">2</div>
                        <div class="left">
                            <span class="time">{{ $t('steps.step2.time') }}</span>
                            <h3 class="name">{{ $t('steps.step2.name') }}</h3>
                            <span class="description">{{ $t('steps.step2.description') }}</span>
                        </div>
                    </div>

                    <!-- Étape 2.b -->
                    <div class="step">
                        <div class="number">2.b</div>
                        <div class="left">
                            <span class="time">{{ $t('steps.step2_b.time') }}</span>
                            <h3 class="name">{{ $t('steps.step2_b.name') }}</h3>
                            <span class="description">{{ $t('steps.step2_b.description') }}</span>
                        </div>
                    </div>

                    <!-- Étape 3 -->
                    <div class="step">
                        <div class="number">3</div>
                        <div class="left">
                            <span class="time">{{ $t('steps.step3.time') }}</span>
                            <h3 class="name">{{ $t('steps.step3.name') }}</h3>
                            <span class="description">{{ $t('steps.step3.description') }}</span>
                        </div>
                    </div>

                    <!-- Étape 4 -->
                    <div class="step">
                        <div class="number">4</div>
                        <div class="left">
                            <span class="time">{{ $t('steps.step4.time') }}</span>
                            <h3 class="name">{{ $t('steps.step4.name') }}</h3>
                            <span class="description">{{ $t('steps.step4.description') }}</span>
                        </div>
                    </div>

                    <!-- Étape 5 -->
                    <div class="step">
                        <div class="number">5</div>
                        <div class="left">
                            <span class="time">{{ $t('steps.step5.time') }}</span>
                            <h3 class="name">{{ $t('steps.step5.name') }}</h3>
                            <span class="description">{{ $t('steps.step5.description') }}</span>
                        </div>
                    </div>

                    <!-- Étape 6 -->
                    <div class="step">
                        <div class="number">6</div>
                        <div class="left">
                            <span class="time">{{ $t('steps.step6.time') }}</span>
                            <h3 class="name">{{ $t('steps.step6.name') }}</h3>
                            <span class="description">{{ $t('steps.step6.description') }}</span>
                        </div>
                    </div>

                    <!-- Étape 7 -->
                    <div class="step">
                        <div class="number">7</div>
                        <div class="left">
                            <span class="time">{{ $t('steps.step7.time') }}</span>
                            <h3 class="name">{{ $t('steps.step7.name') }}</h3>
                            <span class="description">{{ $t('steps.step7.description') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section 3 : FAQ -->
        <div class="container">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('faq_section.small') }}</span>
                    <h2 class="heading-xl">{{ $t('faq_section.title') }}</h2>
                </div>

                <div class="faq">
                    <div class="group" @click="toggleQuestion('1')">
                        <h3 class="question">
                            {{ $t('faq.question1.title') }}
                            <img
                                :class="{ rotated: displayedQuestion === '1' }"
                                alt="chevron"
                                src="~/assets/img/chevron-down.svg"
                            />
                        </h3>
                        <div class="response" :class="{ showed: displayedQuestion === '1' }">
                            {{ $t('faq.question1.answer') }}
                        </div>
                    </div>

                    <div class="group" @click="toggleQuestion('2')">
                        <h3 class="question">
                            {{ $t('faq.question2.title') }}
                            <img
                                :class="{ rotated: displayedQuestion === '2' }"
                                alt="chevron"
                                src="~/assets/img/chevron-down.svg"
                            />
                        </h3>
                        <div class="response" :class="{ showed: displayedQuestion === '2' }">
                            {{ $t('faq.question2.answer') }}
                        </div>
                    </div>

                    <div class="group" @click="toggleQuestion('3')">
                        <h3 class="question">
                            {{ $t('faq.question3.title') }}
                            <img
                                :class="{ rotated: displayedQuestion === '3' }"
                                alt="chevron"
                                src="~/assets/img/chevron-down.svg"
                            />
                        </h3>
                        <div class="response" :class="{ showed: displayedQuestion === '3' }">
                            {{ $t('faq.question3.answer') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>
