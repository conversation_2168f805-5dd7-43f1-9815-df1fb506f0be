<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";

const i18n = useI18n();

useHead({
    title: i18n.t('Cookies')
});
</script>

<template>
    <div id="cookies-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="container">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('cookiesPage.small') }}</span>
                    <h1 class="heading-xl">{{ $t('cookiesPage.headingXL') }}</h1>
                </div>

                <p>
                    {{ $t('cookiesPage.paragraph1') }}
                </p>

                <p>
                    {{ $t('cookiesPage.paragraph2') }}
                </p>

                <p>
                    <strong>{{ $t('cookiesPage.paragraph3Bold') }}</strong>
                    {{ $t('cookiesPage.paragraph3') }}
                </p>

                <h2>{{ $t('cookiesPage.heading2') }}</h2>
                <p>
                    {{ $t('cookiesPage.paragraph4') }}
                </p>

                <h3>{{ $t('cookiesPage.heading3') }}</h3>
                <ul>
                    <li>{{ $t('cookiesPage.listItem1') }}</li>
                    <li>{{ $t('cookiesPage.listItem2') }}</li>
                </ul>

                <p>
                    {{ $t('cookiesPage.paragraph5') }}
                </p>

                <p>
                    {{ $t('cookiesPage.paragraph6') }}
                </p>

                <p>
                    <NuxtLinkLocale to="contact" class="contact-button item">
                        {{ $t('cookiesPage.contactLink') }}
                    </NuxtLinkLocale>
                </p>

                <h2>{{ $t('cookiesPage.heading4') }}</h2>
                <p>
                    {{ $t('cookiesPage.paragraph7') }}
                </p>

                <p>
                    {{ $t('cookiesPage.paragraph8') }}
                </p>

                <p>
                    {{ $t('cookiesPage.paragraph9') }}
                </p>

                <h2>{{ $t('cookiesPage.heading5') }}</h2>
                <p>
                    {{ $t('cookiesPage.paragraph10') }}
                </p>

                <p>
                    {{ $t('cookiesPage.paragraph11') }}
                </p>

                <h3>{{ $t('cookiesPage.heading6') }}</h3>
                <ul>
                    <li>{{ $t('cookiesPage.instructionIE') }}</li>
                    <li>{{ $t('cookiesPage.instructionFirefox') }}</li>
                    <li>{{ $t('cookiesPage.instructionSafari') }}</li>
                    <li>{{ $t('cookiesPage.instructionChrome') }}</li>
                </ul>

                <p>
                    {{ $t('cookiesPage.paragraph12') }}
                </p>

                <p>
                    <NuxtLinkLocale to="index">
                        {{ $t('cookiesPage.returnHome') }}
                    </NuxtLinkLocale>
                </p>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>

<style scoped lang="sass">

</style>