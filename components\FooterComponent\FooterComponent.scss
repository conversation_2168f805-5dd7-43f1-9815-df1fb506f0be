.footer-component {
    display: flex;
    flex-direction: column;

    .top {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        grid-gap: 1.25rem;
        padding-bottom: 4rem;
        border-bottom: 1px solid var(--secondary-color);

        @media (max-width: 1100px) {
            grid-template-columns: 1fr;
        }

        .left {
            .logo {
                flex: 0;
                font-weight: bold;
                font-size: 1.25rem;
            }
        }

        .center {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 1.25rem;

            .category {
                display: flex;
                flex-direction: column;
                gap: 1rem;

                .title {
                    font-size: .875rem;
                    font-weight: 600;
                }

                .links {
                    display: flex;
                    flex-direction: column;
                    gap: .75rem;;
                    font-size: .875rem;
                    color: rgba(0, 0, 0, 0.8);
                    color: #4d4e56;
                    font-weight: 400;

                    .link {
                        all: unset;
                        cursor: pointer;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
    }

    .bottom {
        padding: 2rem 0;
        text-align: center;
        font-size: .75rem;
        opacity: 0.8;
        font-weight: 500;
    }
}