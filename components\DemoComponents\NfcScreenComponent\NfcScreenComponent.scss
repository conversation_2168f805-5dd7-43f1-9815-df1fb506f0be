.nfc-screen-component {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f6f8fc;

    span {
        font-size: 18px;
        color: #0D7BE8;
    }

    .scan-animation-container {
        display: flex;
        align-items: center;
        justify-content: center;

        .scan-animation {
            position: relative;
            height: 124px;
            width: 124px;
            border: 8px solid #0D7BE8;
            border-radius: 50%;
            overflow: hidden;
            transform: scale(0.8);
            perspective: 200px;

            .iphone {
                position: absolute;
                display: flex;
                justify-content: center;
                top: 30px;
                left: 50%;
                height: 200px;
                width: 72px;
                transform: translateX(-50%);
                border-radius: 16px;
                border: 6px solid #0D7BE8;
                background: #fad9d9;
                background: linear-gradient(25deg, #DEF4FD 0%, #DEF4FD 54%, white 54%, white 100%);
                animation: scan 6s infinite;
                background-size: 550% 550%;

                .top {
                    height: 5px;
                    width: 35px;
                    background: #0D7BE8;
                    border-radius: 0 0 6px 6px;
                }
            }

            @keyframes scan {
                0% {
                    transform: translateX(-50%) rotateX(0) scale(1) translateY(0);
                    background-position: 0;
                }
                30% {
                    transform: translateX(-50%) rotateX(30deg) scale(0.9) translateY(-30px);
                    background-position: 50%;
                }
                60% {
                    transform: translateX(-50%) rotateX(0) scale(1) translateY(0);
                    background-position: 0;
                }
                100% {
                    transform: translateX(-50%) rotateX(0) scale(1) translateY(0);
                    background-position: 0;
                }
            }
        }
    }
}