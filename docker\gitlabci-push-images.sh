#!/bin/sh

set -e

docker login -u "$CI_REGISTRY_USER" -p "$CI_JOB_TOKEN" "$CI_REGISTRY"

docker load -i ${IMAGE_NAME_FINAL_ARTIFACT}

docker tag "${IMAGE_NAME_FINAL}:${CI_COMMIT_SHORT_SHA}" "${IMAGE_NAME_FINAL}:${CI_COMMIT_BRANCH_DOCKER}"

if [ "$CI_COMMIT_BRANCH" = "$CI_DEFAULT_BRANCH" ]
then
  version=$(date +%Y-%m-%d)
  echo "Creating docker images with version $version"

  docker tag "${IMAGE_NAME_FINAL}:${CI_COMMIT_SHORT_SHA}" "${IMAGE_NAME_FINAL}:${version}"
  docker tag "${IMAGE_NAME_FINAL}:${CI_COMMIT_SHORT_SHA}" "${IMAGE_NAME_FINAL}:latest"

  docker push "${IMAGE_NAME_FINAL}:${version}"
  docker push "${IMAGE_NAME_FINAL}:latest"
else
  docker push "${IMAGE_NAME_FINAL}:${CI_COMMIT_BRANCH_DOCKER}"
fi

