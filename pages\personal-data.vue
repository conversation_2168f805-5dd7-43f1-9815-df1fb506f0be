<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";
</script>

<template>
    <div id="cookies-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="container">
            <div class="content">
                <div class="content-title">
                    <span class="heading-xl">{{ $t('personalDataPage.headingXL') }}</span>
                </div>

                <p>
                    {{ $t('personalDataPage.paragraph1') }}
                </p>

                <ul>
                    <li>{{ $t('personalDataPage.bullet1') }}</li>
                    <li>{{ $t('personalDataPage.bullet2') }}</li>
                    <li>{{ $t('personalDataPage.bullet3') }}</li>
                    <li>{{ $t('personalDataPage.bullet4') }}</li>
                </ul>

                <p>
                    {{ $t('personalDataPage.paragraph2') }}
                </p>

                <h2>{{ $t('personalDataPage.heading2') }}</h2>
                <p>
                    {{ $t('personalDataPage.paragraph3') }}
                </p>

                <h2>{{ $t('personalDataPage.heading3') }}</h2>
                <p>
                    {{ $t('personalDataPage.paragraph4') }}
                </p>

                <p>
                    {{ $t('personalDataPage.paragraph5') }}
                </p>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>

<style scoped lang="sass">

</style>