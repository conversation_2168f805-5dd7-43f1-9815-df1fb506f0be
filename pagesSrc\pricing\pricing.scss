#pricing-page {
    >.primary {
        background: var(--soft-background-color);
    }

    .quote-cta {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 3rem;
        padding: 3rem 0;

        .content-title {
            flex-grow: 2;
            align-items: flex-start;

            * {
                text-align: left;
            }
        }

        .button {
            flex-shrink: 0;
            margin-top: 0.5rem;
            gap: 1rem;

            .hint {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 400;
                opacity: 0.8;
                font-size: .875rem;
            }

            img {
                height: 16px;
                filter: invert(100%) sepia(0%);
            }
        }

        @media (max-width: 1100px) {
            flex-direction: column;

            .content-title {
                * {
                    text-align: center;
                }
            }
        }
    }

    .rfid-comparison {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 2rem;

        .column {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 1.5rem 2rem;
            border-radius: 1rem;

            &.background {
                background: var(--soft-background-color);
            }
        }

        @media (max-width: 1100px) {
            grid-template-columns: 1fr;
            grid-gap: 2rem;

            .column {
                padding: 1.5rem 1rem;
            }
        }

        .image-header {
            display: flex;
            align-items: center;
            height: 200px;
            justify-content: center;

            img {
                max-width: 100%;
                max-height: 100%;
            }
        }

        .name {
            font-size: 1.25rem;
            font-weight: 500;
            text-align: center;
        }
    }

    .pricing-details {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4rem;
        width: 100%;

        .pricing-detail {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            max-width: 100%;
            scroll-margin-top: 10rem;

            .table-overflow-container {
                overflow: auto;
                max-width: 100%;
            }

            table {
                border-spacing: 0;
                text-align: left;
                white-space: nowrap;
            }

            tr:nth-child(even) {
                background: var(--soft-background-color);
            }

            td, th {
                padding: 1rem 2rem;
                text-align: left !important;
            }
        }
    }

    .tabs {
        display: flex;
        align-items: center;
        background: var(--soft-background-color);
        border-radius: 3rem;
        padding: 0.2rem;
        gap: 0.3rem;

        .tab {
            padding: 0.5em 1rem;
            border-radius: 3rem;
            font-size: 0.875rem;
            cursor: pointer;
            font-weight: 500;

            &:hover {
                background: #f1f1f1;
            }

            &.active {
                background: var(--secondary-color);
            }

            @media (max-width: 400px) {
                font-size: 0.725rem;
            }
        }
    }

    .faq {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        max-width: 800px;
        margin: auto;

        .group {
            display: flex;
            flex-direction: column;
            gap: .5rem;
            padding: 1.5rem;
            border: .1rem solid #c5c5c5;
            border-radius: 1rem;
            cursor: pointer;
            user-select: none;

            .question {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 1.25rem;
                line-height: 1.5;
                font-weight: 500;

                img {
                    height: 16px;

                    &.rotated{
                        transform: rotate(180deg);
                    }
                }
            }

            .response {
                display: none;
                color: #4d4e56;

                &.showed {
                    display: initial;
                }
            }
        }
    }


    .mobile-legend {
        display: none;
        flex-direction: column;

        @media (max-width: 1100px) {
            display: flex;
            font-size: .875rem;
        }
    }
}