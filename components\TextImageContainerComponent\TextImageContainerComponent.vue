<script setup>
const props = defineProps(['small', 'title', 'subtitle', 'description', 'reversed', 'headingLevel'])

// Default to h2 if no heading level is specified
const headingLevel = props.headingLevel || 'h2'
</script>

<template>
    <div class="text-image-container-component" :class="{reversed: reversed}">
        <template v-if="!reversed">
            <div class="left">
                <div class="content-title">
                    <span class="small" v-html="small"></span>
                    <h1 v-if="headingLevel === 'h1'" class="heading-xl" v-html="title"></h1>
                    <h2 v-else-if="headingLevel === 'h2'" class="heading-xl" v-html="title"></h2>
                    <h3 v-else-if="headingLevel === 'h3'" class="heading-xl" v-html="title"></h3>
                    <h4 v-else-if="headingLevel === 'h4'" class="heading-xl" v-html="title"></h4>
                    <h5 v-else-if="headingLevel === 'h5'" class="heading-xl" v-html="title"></h5>
                    <h6 v-else-if="headingLevel === 'h6'" class="heading-xl" v-html="title"></h6>
                    <h2 v-else class="heading-xl" v-html="title"></h2>
                    <span class="description" v-html="subtitle"></span>
                </div>
            </div>
            <div class="right">
                <slot></slot>
            </div>
        </template>
        <template v-else>
            <div class="left">
                <slot></slot>
            </div>
            <div class="right">
                <div class="content-title">
                  <span class="small" v-html="small"></span>
                  <h1 v-if="headingLevel === 'h1'" class="heading-xl" v-html="title"></h1>
                  <h2 v-else-if="headingLevel === 'h2'" class="heading-xl" v-html="title"></h2>
                  <h3 v-else-if="headingLevel === 'h3'" class="heading-xl" v-html="title"></h3>
                  <h4 v-else-if="headingLevel === 'h4'" class="heading-xl" v-html="title"></h4>
                  <h5 v-else-if="headingLevel === 'h5'" class="heading-xl" v-html="title"></h5>
                  <h6 v-else-if="headingLevel === 'h6'" class="heading-xl" v-html="title"></h6>
                  <h2 v-else class="heading-xl" v-html="title"></h2>
                  <span class="description" v-html="subtitle"></span>
                </div>
            </div>
        </template>
    </div>
</template>

<style lang="scss">

.text-image-container-component {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 4rem;

    @media (max-width: 1100px) {
        display: flex;
        flex-direction: column;
        gap: 0;

        &.reversed {
            flex-direction: column-reverse;
        }

        .content-title {
            margin-bottom: 1rem;
        }
    }

    .right, .left {
        display: flex;
        align-items: center;

        .content-title * {
            text-align: left !important;
        }

        img {
            height: auto;
            width: 100%;
            border-radius: 25px;
        }

        .description {
            font-size: 1rem;
            line-height: 1.5;
            color: #4d4e56;
        }
    }
}

.text-images {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

</style>