<script setup>
import { computed } from 'vue'

const props = defineProps(['small', 'title', 'subtitle', 'description', 'reversed', 'headingLevel'])

// Default to h2 if no heading level is specified
const headingTag = computed(() => props.headingLevel || 'h2')
</script>

<template>
    <div class="text-image-container-component" :class="{reversed: reversed}">
        <template v-if="!reversed">
            <div class="left">
                <div class="content-title">
                    <span class="small" v-html="props.small"></span>
                    <component :is="headingTag" class="heading-xl" v-html="props.title"></component>
                    <span class="description" v-html="props.subtitle"></span>
                </div>
            </div>
            <div class="right">
                <slot></slot>
            </div>
        </template>
        <template v-else>
            <div class="left">
                <slot></slot>
            </div>
            <div class="right">
                <div class="content-title">
                  <span class="small" v-html="props.small"></span>
                  <component :is="headingTag" class="heading-xl" v-html="props.title"></component>
                  <span class="description" v-html="props.subtitle"></span>
                </div>
            </div>
        </template>
    </div>
</template>

<style lang="scss">

.text-image-container-component {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 4rem;

    @media (max-width: 1100px) {
        display: flex;
        flex-direction: column;
        gap: 0;

        &.reversed {
            flex-direction: column-reverse;
        }

        .content-title {
            margin-bottom: 1rem;
        }
    }

    .right, .left {
        display: flex;
        align-items: center;

        .content-title * {
            text-align: left !important;
        }

        img {
            height: auto;
            width: 100%;
            border-radius: 25px;
        }

        .description {
            font-size: 1rem;
            line-height: 1.5;
            color: #4d4e56;
        }
    }
}

.text-images {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

</style>