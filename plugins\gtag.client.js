export default defineNuxtPlugin((nuxtApp) => {
    const acceptedCookies = ref(localStorage.getItem("accepted-cookies"));
    const { gtagId } = useRuntimeConfig().public;

    // Initialize dataLayer immediately (required for gtag to work)
    window.dataLayer = window.dataLayer || [];

    function gtag() {
        window.dataLayer.push(arguments);
    }

    window.gtag = gtag;

    // ALWAYS load the gtag script first, regardless of consent status
    // This ensures updateGoogleConsent() will work when called from cookie banner
    loadGtagScript(acceptedCookies.value === 'true');

    function loadGtagScript(hasConsent) {
        if (!hasConsent) {
            // Initialize consent mode BEFORE loading script
            gtag('consent', 'default', {
                'analytics_storage': 'denied',
                'ad_storage': 'denied',
                'ad_user_data': 'denied',
                'ad_personalization': 'denied'
            });
        }

        useHead({
            script: [
                {
                    src: `https://www.googletagmanager.com/gtag/js?id=${gtagId}`,
                    async: true,
                    onload: () => {
                        console.log("✅ gtag script loaded successfully");

                        gtag("js", new Date());
                        gtag("config", gtagId, {
                            send_page_view: hasConsent // Only send page view if consent granted
                        });

                        if (hasConsent) {
                            trackDarkSocial();
                        }
                    },
                    onerror: (error) => {
                        console.error("❌ Failed to load gtag script:", error);
                    }
                }
            ]
        });
    }

    function trackDarkSocial() {
        setTimeout(() => {
            const referrer = document.referrer;
            const landingPage = window.location.pathname;
            const isDeepPage = landingPage !== '/fr' && landingPage !== '/' && landingPage !== '';

            if (!referrer && isDeepPage) {
                gtag("event", "potential_dark_social", {
                    landing_page: landingPage,
                    user_agent: navigator.userAgent.includes('Mobile') ? 'mobile' : 'desktop',
                    custom_parameter_1: 'direct_to_deep_page'
                });
            }
        }, 1000);
    }

    // Expose function to update consent (called from cookie banner)
    window.updateGoogleConsent = function(granted) {
        if (granted) {
            gtag('consent', 'update', {
                'analytics_storage': 'granted',
                'ad_storage': 'granted',
                'ad_user_data': 'granted',
                'ad_personalization': 'granted'
            });

            // Send page view now that consent is granted - this triggers session_start
            gtag("event", "page_view");

            // Verify dataLayer after consent update
            setTimeout(() => {
            }, 500);
        }
    };
});