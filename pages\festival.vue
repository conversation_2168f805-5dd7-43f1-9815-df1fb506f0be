<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";
import TextImageContainerComponent from "~/components/TextImageContainerComponent/TextImageContainerComponent.vue";

const i18n = useI18n();

useHead({
    title: i18n.t('festival.hero.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('festival.hero.subtitle')
        },
        {
            property: 'og:title',
            content: i18n.t('festival.hero.title')
        },
        {
            property: 'og:description',
            content: i18n.t('festival.hero.subtitle')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ]
});

</script>

<style lang="sass">
@use '../pagesSrc/festival/festival.scss' as *
</style>

<template>
    <div id="festival-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="first container primary">
            <div class="content">
                <div class="hero-banner">
                    <div class="left">
                        <div>
                            <span class="title heading-2xl">{{ $t('festival.hero.title') }}</span>
                        </div>

                        <span class="subtitle heading-sm">{{ $t('festival.hero.subtitle') }}</span>

                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <div class="trusted-by-companies">
                            <span class="title">{{ $t('festival.hero.trustedBy') }}</span>

                            <div class="companies">
                                <NuxtImg class="darken" format="webp" alt="polysonnades" :src="'/polysonnades.png'" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="undergrange" :src="'/undergrange.jpg'" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="Choral events" src="/choral-events-long.png" sizes="xs:150px md:150px" />
                            </div>
                        </div>
                    </div>

                    <div class="illustration">
                        <NuxtImg format="webp" alt="festival-illustration" width="680" height="491" :src="'/festival-' + $i18n.locale + '.png'" sizes="xs:420px md:600px" />
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="text-images">
                    <TextImageContainerComponent
                        :small="$t('festival.features.quickSetup.small')"
                        :title="$t('festival.features.quickSetup.title')"
                        :subtitle="$t('festival.features.quickSetup.subtitle')"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/sunmi/p2-lite-methods.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('festival.features.multiPoint.small')"
                        :title="$t('festival.features.multiPoint.title')"
                        :subtitle="$t('festival.features.multiPoint.subtitle')"
                        :reversed="true"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/interface-point-of-sale.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('festival.features.customizedSupport.small')"
                        :title="$t('festival.features.customizedSupport.title')"
                        :subtitle="$t('festival.features.customizedSupport.subtitle')"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/personalisation-flat.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>
                </div>
            </div>
        </div>

        <div class="container primary">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('festival.clients.titleSmall') }}</span>
                    <span class="heading-xl">{{ $t('festival.clients.title') }}</span>
                </div>

                <div class="testimonials">
                    <div class="testimonial">
                        <div class="comment">{{ $t('festival.clients.testimonial1.comment') }}</div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="testimonial1" width="36" height="36" src="/choral-event.jpg" sizes="xs:36px md:36px" />
                                <div class="data">
                                    <span class="name">{{ $t('festival.clients.testimonial1.name') }}</span>
                                    <span class="company">{{ $t('festival.clients.testimonial1.company') }}</span>
                                </div>
                            </div>
                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" v-for="i in 5" :key="i" />
                            </div>
                        </div>
                    </div>

                    <div class="testimonial">
                        <div class="comment">{{ $t('festival.clients.testimonial2.comment') }}</div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="testimonial2" width="36" height="36" src="/polysonnades.png" sizes="xs:36px md:36px" />
                                <div class="data">
                                    <span class="name">{{ $t('festival.clients.testimonial2.name') }}</span>
                                    <span class="company">{{ $t('festival.clients.testimonial2.company') }}</span>
                                </div>
                            </div>
                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" v-for="i in 5" :key="i" />
                            </div>
                        </div>
                    </div>

                    <div class="testimonial">
                        <div class="comment">{{ $t('festival.clients.testimonial3.comment') }}</div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="testimonial3" width="36" height="36" src="/undergrange.jpg" sizes="xs:36px md:36px" />
                                <div class="data">
                                    <span class="name">{{ $t('festival.clients.testimonial3.name') }}</span>
                                    <span class="company">{{ $t('festival.clients.testimonial3.company') }}</span>
                                </div>
                            </div>
                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" v-for="i in 5" :key="i" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-bubble">
                    <div class="content-title">
                        <span class="heading-xl">{{ $t('festival.readyToSwitch.title') }}</span>
                    </div>

                    <div class="big-buttons">
                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <NuxtLinkLocale to="/contact" class="grey button">
                            {{ $t('festival.readyToSwitch.contact') }}
                        </NuxtLinkLocale>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>