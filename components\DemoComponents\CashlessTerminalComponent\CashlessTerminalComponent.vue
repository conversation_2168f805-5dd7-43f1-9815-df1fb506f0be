<script setup>

import { ref } from 'vue';
const emit = defineEmits(['payment'])


class Cart {
    items = [];

    get total() {
        let total = 0;
        for (let item of this.items) {
            total += item.price;
        }
        return total;
    }

    add(product) {
        this.items.push(product);
    }

    clean(){
        this.items = [];
    }

    getQuantityById(id ){
        let total = 0;
        for(let item of this.items){
            if(item.id === id){
                total++;
            }
        }
        return total;
    }
}

let currentCategory = ref(null);

let drinkItems = [
    {
        id: 'vin-rouge',
        name: 'Vin rouge',
        icon: '🍷',
        price: 4,
        category: 'WINE',
        available: true
    },
    {
        id: 'vin-blanc',
        name: 'Vin blanc',
        icon: '🥂',
        price: 4,
        category: 'WINE',
        available: true
    },
    {
        id: 'pinta',
        name: 'Pinta',
        icon: '🍺',
        price: 4,
        category: 'BEER',
        available: true
    },
    {
        id: 'hawaiiano',
        name: '<PERSON><PERSON>',
        icon: '🍹',
        price: 5,
        category: 'COCKTAIL',
        available: true
    },
    {
        id: 'cocktail',
        name: 'Cocktail',
        icon: '🍸',
        price: 7,
        category: 'COCKTAIL',
        available: true
    },
    {
        id: 'tequila',
        name: 'Tequila',
        icon: '🍹',
        price: 4,
        category: 'COCKTAIL',
        available: true
    },
    // {
    //     id: 'licor',
    //     name: 'Licor',
    //     icon: '🥃',
    //     price: 4,
    //     category: DrinkCategory.SPIRIT,
    //     available: true
    // },
    // {
    //     id: 'whiskey',
    //     name: 'Whiskey',
    //     icon: '🥃',
    //     price: 5,
    //     category: DrinkCategory.SPIRIT,
    //     available: true
    // },
    // {
    //     id: 'mojito',
    //     name: 'Mojito',
    //     icon: '🍸',
    //     price: 6,
    //     category: DrinkCategory.COCKTAIL,
    //     available: true
    // },
    // {
    //     id: 'botella',
    //     name: 'Botella',
    //     icon: '🍺',
    //     price: 4,
    //     category: DrinkCategory.BEER,
    //     available: true
    // },
    // {
    //     id: 'zumo',
    //     name: 'Zumo',
    //     icon: '🍊',
    //     price: 3,
    //     category: DrinkCategory.SOFT,
    //     available: true
    // },
    // {
    //     id: 'cafe',
    //     name: 'Café',
    //     icon: '☕',
    //     price: 2,
    //     category: DrinkCategory.COFFEE,
    //     available: true
    // }
];

let cart = ref(new Cart());

function getCountCategory(category){
    let total = 0;
    for(let product of drinkItems){
        if(product.category === category){
            total++;
        }
    }
    return total;
}

function getFilterProducts() {
    let products = [];
    if(currentCategory.value === null) return drinkItems;

    for(let product of drinkItems){
        if(product.category === currentCategory.value){
            products.push(product);
        }
    }
    return products;
}

function payment() {
    emit('payment', cart.value.total);
}


</script>

<style lang="sass" scoped>
@use './CashlessTerminalComponent.scss' as *
</style>

<template>
<div class="interface">
	<div class="container">
		<div class="header">
			<button class="back-button" aria-label="Retour"></button>
			<div class="page-title">
                {{ $t('demo.sell') }}
            </div>
			<div class="action-icons" @click="cart.clean()">🗑️</div>
		</div>

		<div class="filter-tabs unselectable">
			<div class="tab" :class="{active: currentCategory === null}" @click="currentCategory = null"> {{ $t('demo.all') }} </div>
			<template v-for="category of ['WINE', 'BEER', 'COCKTAIL']">
				<div class="tab" @click="currentCategory = category" :class="{active: currentCategory === category}" v-if="getCountCategory(category) > 0">
                    {{ category === 'WINE' ? $t('demo.wine') : category === 'BEER' ? $t('demo.beer') : $t('demo.cocktail') }}
                </div>
			</template>
		</div>

		<div class="grid">
			<div class="item unselectable" v-for="(product, index) of getFilterProducts()" @click="cart.add(product)">
                <div class="vibrate" v-if="index === 0 && cart.total === 0"></div>
				<div class="icon"> {{ product.icon }} </div>
				<div class="name"> {{ product.name }} </div>
				<div class="price"> {{ product.price }}€ </div>
				<div class="quantity" v-if="cart.getQuantityById(product.id) > 0">
                    {{ cart.getQuantityById(product.id) }}
                </div>
			</div>
		</div>
	</div>

	<div class="footer">
		<div class="total"></div>
		<div class="total">-{{cart.total}}€</div>
		<button class="checkout-button" :class="{disabled: cart.total === 0}" @click="payment()">
            {{ $t('demo.validate') }}
        </button>
	</div>
	</div>
</template>