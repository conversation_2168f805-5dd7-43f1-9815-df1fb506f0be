{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "serve": "npx http-server -p 3009 -a 0.0.0.0 .output/public"}, "dependencies": {"@groupk/vite-vfc-intellij": "^1.0.2", "@nuxt/image": "^1.9.0", "@nuxtjs/i18n": "^9.1.1", "@nuxtjs/sitemap": "^7.2.3", "@rollup/plugin-html": "^1.1.0", "nuxt": "^3.15.1", "sass": "^1.83.1", "vue-facing-decorator": "^3.0.3", "vue-router": "latest"}}