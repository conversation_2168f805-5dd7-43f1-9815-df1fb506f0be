.interface {
    position: relative;
    max-height: 100%;
    height: 100%;

    .unselectable {
        user-select: none;
        -moz-user-select: none;
        -khtml-user-select: none;
        -webkit-user-select: none;
        -o-user-select: none;
    }

    .container {
        max-width: 100%;
        margin: 0 auto;
        background-color: #f4f6f8;
        max-height: 90%;
        height: 100%;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 10px;
        border-bottom: 1px solid #e0e0e0;
        margin-bottom: 15px;

        .action-icons {
            color: white;
            background-color: var(--secondary-color);
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 14px;
            cursor: pointer;
        }

        .back-button {
            border: none;
            background-color: transparent;
        }
    }


    .page-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }

    .action-icons {
        display: flex;
        gap: 10px;
    }

    .filter-tabs {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;
        overflow-x: auto;
        padding-bottom: 5px;
    }

    .tab {
        cursor: pointer;
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 16px;
        padding: 5px 15px;
        font-size: 14px;
        white-space: nowrap;

        &:hover {
            background-color: #eeeeee;
        }
    }

    .tab.active {
        background-color: var(--primary-color);
        color: white;
        border: 1px solid var(--primary-color);
    }

    .grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        max-height: 80%;

    }

    .item {
        cursor: pointer;
        background-color: white;
        border-radius: 8px;
        padding: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        overflow-y: auto;
        position: relative;

        .vibrate {
            position: absolute;
            top: 10px;
            left: 10px;
        }

        &:hover {
            background-color: #eeeeee;
        }

        .icon {
            width: 40px;
            height: 40px;
            margin-bottom: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 40px;
        }
    }


    .name {
        font-size: 12px;
        text-align: center;
        margin-bottom: 5px;
    }

    .price {
        font-size: 14px;
        font-weight: bold;
        color: #5c6bc0;
    }

    .quantity {
        position: absolute;
        top: 2px;
        right: 4px;
        background-color: #263380;
        height: 20px;
        width: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100px;
        font-size: 10px;
        color: white;
        font-weight: 800;
    }

    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        padding: 10px 15px;
        border-top: 1px solid #e0e0e0;
    }

    .total {
        font-size: 18px;
        font-weight: bold;
        color: #5c6bc0;
        display: flex;
        align-items: center;
    }

    .checkout-button {
        background-color: #4caf50;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 20px;
        cursor: pointer;

        &.disabled {
            pointer-events: none;
            opacity: 0.5;
        }
    }

    .vibrate {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #2ab9d9;
        transform: scale(1);
        box-shadow: 0 0 0 rgba(0, 0, 0, 1);
        animation: anim-vibrate 2s infinite;

        @keyframes anim-vibrate {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(42, 185, 217, 0.7);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 0.6rem rgba(0, 0, 0, 0);
            }

            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
            }
        }
    }
}
