#simulation-page {

    > .primary {
        background: var(--soft-background-color);
    }

    .question-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
        margin-top: 4rem;

        &.done {
            display: none;
        }

        &.coming {
            display: none;
        }

        .buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
    }

    .question {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;

        .step {
            font-size: .875rem;
            opacity: 0.8;
        }

        .title {
            text-align: center;
            font-weight: normal;
        }
    }

    .sub-question {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;

        .title {
            font-size: 1.25rem;
            text-align: center;
        }
    }

    .answers {
        display: flex;
        align-items: stretch;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
        width: 100%;

        .input-group {
            grid-column: 1/3;
        }

        .answer {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: .75rem;
            padding: 2rem 1rem;
            width: 16vw;
            border: 2px solid #edecec;
            background: var(--soft-background-color);
            border-radius: .75rem;
            cursor: pointer;
            user-select: none;

            .icon {
                display: none;
                height: 42px;
                width: 42px;
                background: #D9D9D9;
                border-radius: 12px;
            }

            .content {
                all: unset;
                flex-grow: 2;

                .title {
                    text-align: center;
                    font-size: 1rem;
                    font-weight: 500;
                }

                .subtitle {
                    text-align: center;
                    font-size: 1rem;
                    color: rgba(0, 0, 0, 0.8)
                }
            }

            img {
                height: 24px;
            }
        }

        @media (max-width: 1100px) {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 1rem;

            .answer {
                width: auto;
            }
        }
    }

    .input-group {
        display: flex;
        flex-direction: column;
        gap: .2rem;

        label {
            opacity: 0.8;
        }

        input {
            all: unset;
            background: #ededed;
            border-radius: .5rem;
            padding: 0.5rem 1rem;
            box-sizing: border-box;
        }

        @media (max-width: 900px) {
            width: 100%;

            input {
                width: 100%;
            }
        }
    }

    .result {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;

        .result-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 1rem;
            width: 100%;

            @media (max-width: 900px) {
                grid-template-columns: 1fr;
                grid-gap: 2rem;
            }

            .item {
                display: flex;
                flex-direction: column;
                align-items: center;

                .amount {
                    font-size: 4rem;
                    font-weight: 500;

                    .small {
                        font-size: 1.5rem;
                    }

                    @media (max-width: 900px) {
                        font-size: 3rem;

                        .small {
                            font-size: 1rem;
                        }
                    }
                }

                .subtitle {
                    font-size: 1.25rem;

                    @media (max-width: 900px) {
                        font-size: 1rem;
                    }
                }
            }
        }

        .button {
            margin-top: 4rem;
        }
    }

    .white-icon {
        filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(108deg) brightness(104%) contrast(104%);
    }

    .error {
        color: red;
    }
}