$green: #4CAF50;
.screen-container {
    width: 100%;
    height: 100%;
    background-color: $green;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;

    .check-container {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background-color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        position: relative;
    }

    .check-mark {
        stroke-dasharray: 1000;
        stroke-dashoffset: 1000;
        animation: dash 1.5s ease-in-out forwards;
        stroke: #4CAF50;
        stroke-width: 8;
        fill: none;
    }


    .confetti {
        position: absolute;
        width: 15px;
        height: 15px;
        background-color: #FFF;
        opacity: 0;
    }

    .message {
        font-size: 32px;
        font-weight: bold;
        color: white;
        margin-bottom: 20px;
        opacity: 0;
        transform: translateY(20px);
        animation: fadeIn 0.3s ease-out .2s forwards;
    }

    .amount {
        color: white;
        font-size: 48px;
        font-weight: bold;
        margin-bottom: 10px;
        opacity: 0;
        transform: translateY(20px);
        animation: fadeIn 0.3s ease-out .4s forwards;
    }

    .date {
        color: white;
        font-size: 18px;
        opacity: 0;
        transform: translateY(20px);
        animation: fadeIn 0.3s ease-out .6s forwards;
    }

    .actions {
        width: 100%;
        box-sizing: border-box;
        padding: 0 10px;
        position: absolute;
        bottom: 0;

        .action {
            cursor: pointer;
            border-radius: 10px;
            margin-bottom: 1rem;
            box-sizing: border-box;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            color: $green;
            width: 100%;

            &:hover {
                background-color: #eeeeee;
            }
        }
    }


}

@keyframes dash {
    to {
        stroke-dashoffset: 0;
    }
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


@keyframes confettiFall {
    0% {
        opacity: 1;
        transform: translateY(-100%) rotate(0deg);
    }
    100% {
        opacity: 0;
        transform: translateY(1000%) rotate(360deg);
    }
}