<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import {UserIdRepository} from "../class/UserIdRepository.ts";

const i18n = useI18n();

useHead({
    title: i18n.t('simulation.seo.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('simulation.seo.description')
        },
        {
            property: 'og:title',
            content: i18n.t('simulation.seo.title')
        },
        {
            property: 'og:description',
            content: i18n.t('simulation.seo.description')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ]
});

let step = ref(1);

let formUid = guidGenerator()

let needsType = ref(null);
let startDate = ref(null);
let endDate = ref(null);
let deviceCount = ref(null);
let rfidCount = ref(null);
let rfidType = ref(null);
let personalize = ref(null);

let training = ref(false);
let helpCenter = ref(false);

let contactEmail = ref(null);
let contactPhone = ref(null);

let showResults = ref(false);

let error = ref(null);

onMounted(() => {
    const today = new Date();
    startDate.value = (today.toISOString()).split('T')[0];
    today.setDate(today.getDate() + 1);
    endDate.value = (today.toISOString()).split('T')[0];
});

watch(step, () => {
    sendToAirtable();
});

watch(showResults, () => {
    sendToAirtable();
});

function guidGenerator() {
    const S4 = function () {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    };
    return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
}

function sendToAirtable() {
    const userIdRepository = new UserIdRepository();
    fetch('https://pro.weecop.fr/cashl.es/form.php', {
        method: 'POST',
        body: JSON.stringify({
            'fields': {
                'fldk8PnwBUMkg1MYn': formUid,
                'fldT4pis6Lv4kJpfj': userIdRepository.getUserId(),
                'fldVzxkL3hjQmSOGB': startDate.value,
                'flde8jpL7PPAxkDY4': endDate.value,
                'fldD890HPV3fin8gL': deviceCount.value,
                'fld9NyHb1Yw0CPTXO': needsType.value,
                'fldhKx0FvjQ2UXEgf': rfidCount.value,
                'fldZZgdQ4dW7NWOZW': rfidType.value,
                'fld7fKJHSogqAQxUC': personalize.value,
                'fld13DAH7aUaxQ5Va': training.value,
                'fldL3JTTs5wb7ZPPH': helpCenter.value,
                'fld9ZXqx8693MJeVs': contactEmail.value,
                'fldMYSVFJ7VrB8Dum': contactPhone.value,
            }
        })
    });
}

function validateQuantity() {
    error.value = null;
    if(needsType.value === 'EVENT') {
        if(!startDate.value) {
            error.value = 'START_DATE';
            return;
        }
        if(!endDate.value) {
            error.value = 'END_DATE';
            return;
        }
    }

    if (rfidCount.value === null || rfidCount.value < 500) {
        error.value = 'QUANTITY';
    } else {
        if(window.gtag) {
            window.gtag('event', 'gave_quantity', {
                parameter_name: rfidCount.value ?? 0,
            });
        }

        deviceCount.value = (rfidCount.value ?? 10) / 10;

        step.value++;
    }
}

function validateDeviceCount() {
    error.value = null;

    if (deviceCount.value === null || deviceCount.value < 1) {
        error.value = 'DEVICE_COUNT';
    } else {
        step.value++;
    }
}

function validateContactData() {
    error.value = null;

    if (!contactEmail.value || !contactEmail.value
        .toLowerCase()
        .match(
            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        )
    ) {
        error.value = 'EMAIL';
    } else if (!contactPhone.value || contactPhone.value.length < 3) {
        error.value = 'PHONE';
    } else {
        showResults.value = true;
    }
}

function getPricePerChip(quantity, chipType, personalize) {
    // Grilles de tarifs pour chaque catégorie
    const priceGrids = {
        'CARD': [
            {minQty: 500, maxQty: 999, personalized: 2.40, myWeecop: 1.72},
            {minQty: 1000, maxQty: 4999, personalized: 1.72, myWeecop: 1.50},
            {minQty: 5000, maxQty: 9999, personalized: 1.50, myWeecop: 1.20},
            {minQty: 10000, maxQty: 24999, personalized: 1.20, myWeecop: 1.00},
            {minQty: 25000, maxQty: Infinity, personalized: 1.00, myWeecop: 0.89},
        ],
        'EVENT': [
            {minQty: 500, maxQty: 999, personalized: 2.40, myWeecop: 1.72},
            {minQty: 1000, maxQty: 4999, personalized: 1.72, myWeecop: 1.50},
            {minQty: 5000, maxQty: 9999, personalized: 1.50, myWeecop: 1.20},
            {minQty: 10000, maxQty: 24999, personalized: 1.20, myWeecop: 1.00},
            {minQty: 25000, maxQty: Infinity, personalized: 1.00, myWeecop: 0.89},
        ],
        'RESORT': [
            {minQty: 500, maxQty: 999, personalized: 2.40, myWeecop: 1.72},
            {minQty: 1000, maxQty: 4999, personalized: 1.72, myWeecop: 1.50},
            {minQty: 5000, maxQty: 9999, personalized: 1.50, myWeecop: 1.20},
            {minQty: 10000, maxQty: 24999, personalized: 1.20, myWeecop: 1.00},
            {minQty: 25000, maxQty: Infinity, personalized: 1.00, myWeecop: 0.89},
        ],
    };

    // Recherche dans la grille de la catégorie choisie
    const priceGrid = priceGrids[chipType];
    const price = priceGrid.find(
        (tier) => quantity >= tier.minQty && quantity <= tier.maxQty
    );

    // Vérification si la quantité est inférieure au minimum
    if (!price) {
        return 0;
    }

    // Retour du prix unitaire
    return price[personalize ? 'personalized' : 'myWeecop'];
}


function getSimulationResults() {
    if (step.value !== 7 || rfidCount.value === null || rfidType.value === null || personalize.value === null) return {
        fees: 0,
        rfidChips: 0,
        installationFees: 0,
        subscription: 0
    };

    return {
        fees: 0,
        rfidChips: getPricePerChip(rfidCount.value, rfidType.value, personalize.value) * rfidCount.value,
        installationFees: 0,
        subscription: needsType.value === 'PERMANENT' ? 49 : 0,
    }
}

</script>

<style lang="sass" scoped>
@use '../pagesSrc/simulation/simulation.scss' as *
</style>

<template>
    <div id="simulation-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="container">
            <!-- BEFORE RESULTS -->
            <div class="content" v-if="!showResults">
                <div class="content-title" @click="sendToAirtable()">
                    <span class="small">{{ $t('simulation.title') }}</span>
                    <span class="heading-xl">
                        {{ $t('simulation.subtitle') }}
                    </span>
                </div>

                <div class="form">
                    <!-- STEP 1 -->
                    <div class="question-group" :class="{ done: step > 1 }">
                        <div class="question">
                            <span class="step"> {{ $t('simulation.stepXofY', {x: 1, y: 6}) }} </span>
                            <span class="heading-sm title">{{ $t('simulation.questionNeedsType') }}</span>
                        </div>

                        <div class="answers">
                            <div
                                class="answer"
                                :class="{ selected: needsType === 'EVENT' }"
                                @click="needsType = 'EVENT'"
                            >
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">{{ $t('simulation.event.title') }}</div>
                                    <div class="subtitle">{{ $t('simulation.event.subtitle') }}</div>
                                </div>
                                <img
                                    alt="radio-button"
                                    v-if="needsType === 'EVENT'"
                                    src="~/assets/img/circle-dot.svg"
                                />
                                <img
                                    alt="radio-button"
                                    v-else
                                    src="~/assets/img/circle.svg"
                                />
                            </div>

                            <div
                                class="answer"
                                :class="{ selected: needsType === 'PERMANENT' }"
                                @click="needsType = 'PERMANENT'"
                            >
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">{{ $t('simulation.permanent.title') }}</div>
                                    <div class="subtitle">{{ $t('simulation.permanent.subtitle') }}</div>
                                </div>
                                <img
                                    alt="radio-button"
                                    v-if="needsType === 'PERMANENT'"
                                    src="~/assets/img/circle-dot.svg"
                                />
                                <img
                                    alt="radio-button"
                                    v-else
                                    src="~/assets/img/circle.svg"
                                />
                            </div>
                        </div>

                        <button
                            class="primary button"
                            :class="{ disabled: needsType === null }"
                            @click="step++"
                        >
                            {{ $t('simulation.next') }}
                        </button>
                    </div>

                    <!-- STEP 2 -->
                    <div class="question-group" :class="{ coming: step < 2, done: step > 2 }">
                        <div class="question">
                            <span class="step">{{ $t('simulation.stepXofY', {x: 2, y: 6}) }}</span>
                            <span class="heading-sm title">
                            {{ $t('simulation.questionRfidCount') }}
                          </span>
                        </div>

                        <div class="answers">
                            <template v-if="needsType === 'EVENT'">
                                <div class="input-group">
                                    <label>{{ $t('simulation.startDateLabel') }}</label>
                                    <input
                                        type="date"
                                        v-model="startDate"
                                    />
                                </div>
                                <div class="input-group">
                                    <label>{{ $t('simulation.endDateLabel') }}</label>
                                    <input
                                        type="date"
                                        v-model="endDate"
                                    />
                                </div>
                            </template>

                            <div class="input-group">
                                <label>{{ $t('simulation.estimateLabel') }}</label>
                                <input
                                    type="number"
                                    v-model="rfidCount"
                                    min="0" inputmode="numeric" pattern="[0-9]*"
                                    :placeholder="$t('simulation.estimatePlaceholder')"
                                />
                            </div>
                        </div>

                        <div class="error" v-if="error === 'START_DATE'">
                            {{ $t('simulation.errors.startDateRequired') }}
                        </div>
                        <div class="error" v-if="error === 'END_DATE'">
                            {{ $t('simulation.errors.endDateRequired') }}
                        </div>

                        <div class="error" v-if="error === 'START_DATE'">
                            La date de début est requise
                        </div>
                        <div class="error" v-if="error === 'END_DATE'">
                            La date de fin est requise
                        </div>

                        <div class="error" v-if="error === 'QUANTITY'">
                            {{ $t('simulation.errors.quantity') }}
                        </div>

                        <div class="buttons">
                            <button class="secondary button" @click="step--">
                                {{ $t('simulation.back') }}
                            </button>
                            <button
                                class="primary button"
                                :class="{ disabled: !rfidCount }"
                                @click="validateQuantity()"
                            >
                                {{ $t('simulation.next') }}
                            </button>
                        </div>
                    </div>

                    <!-- STEP 3 -->
                    <div class="question-group" :class="{ coming: step < 3, done: step > 3 }">
                        <div class="question">
                            <span class="step">{{ $t('simulation.stepXofY', {x: 3, y: 6}) }}</span>
                            <span class="heading-sm title">
                                {{ $t('simulation.questionRfidType') }}
                            </span>
                        </div>

                        <div class="answers">
                            <div
                                class="answer"
                                :class="{ selected: rfidType === 'EVENT' }"
                                @click="rfidType = 'EVENT'"
                            >
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">{{ $t('simulation.rfidTypeEvent.title') }}</div>
                                    <div class="subtitle">{{ $t('simulation.rfidTypeEvent.subtitle') }}</div>
                                </div>
                                <img
                                    alt="radio-button"
                                    v-if="rfidType === 'EVENT'"
                                    src="~/assets/img/circle-dot.svg"
                                />
                                <img
                                    alt="radio-button"
                                    v-else
                                    src="~/assets/img/circle.svg"
                                />
                            </div>

                            <div
                                class="answer"
                                :class="{ selected: rfidType === 'RESORT' }"
                                @click="rfidType = 'RESORT'"
                            >
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">{{ $t('simulation.rfidTypeResort.title') }}</div>
                                    <div class="subtitle">{{ $t('simulation.rfidTypeResort.subtitle') }}</div>
                                </div>
                                <img
                                    alt="radio-button"
                                    v-if="rfidType === 'RESORT'"
                                    src="~/assets/img/circle-dot.svg"
                                />
                                <img
                                    alt="radio-button"
                                    v-else
                                    src="~/assets/img/circle.svg"
                                />
                            </div>

                            <div
                                class="answer"
                                :class="{ selected: rfidType === 'CARD' }"
                                @click="rfidType = 'CARD'"
                            >
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">{{ $t('simulation.rfidTypeCard.title') }}</div>
                                </div>
                                <img
                                    alt="radio-button"
                                    v-if="rfidType === 'CARD'"
                                    src="~/assets/img/circle-dot.svg"
                                />
                                <img
                                    alt="radio-button"
                                    v-else
                                    src="~/assets/img/circle.svg"
                                />
                            </div>
                        </div>

                        <div class="buttons">
                            <button class="secondary button" @click="step--">
                                {{ $t('simulation.back') }}
                            </button>
                            <button
                                class="primary button"
                                :class="{ disabled: rfidType === null }"
                                @click="step++"
                            >
                                {{ $t('simulation.next') }}
                            </button>
                        </div>
                    </div>

                    <!-- STEP 4 -->
                    <div class="question-group" :class="{ coming: step < 4, done: step > 4 }">
                        <div class="question">
                            <span class="step">{{ $t('simulation.stepXofY', {x: 4, y: 6}) }}</span>
                            <span class="heading-sm title">
                                {{ $t('simulation.questionPersonalize') }}
                            </span>
                        </div>

                        <div class="answers">
                            <div
                                class="answer"
                                :class="{ selected: personalize === true }"
                                @click="personalize = true"
                            >
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">{{ $t('simulation.yes') }}</div>
                                    <div class="subtitle">
                                        {{ getPricePerChip(rfidCount ?? 500, rfidType ?? 'CARD', true) }}
                                        € {{ $t('simulation.subtitlePerChip') }}
                                    </div>
                                </div>
                                <img
                                    alt="radio-button"
                                    v-if="personalize === true"
                                    src="~/assets/img/circle-dot.svg"
                                />
                                <img
                                    alt="radio-button"
                                    v-else
                                    src="~/assets/img/circle.svg"
                                />
                            </div>

                            <div
                                class="answer"
                                :class="{ selected: personalize === false }"
                                @click="personalize = false"
                            >
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">{{ $t('simulation.no') }}</div>
                                    <div class="subtitle">
                                        {{ getPricePerChip(rfidCount ?? 500, rfidType ?? 'CARD', false) }}
                                        € {{ $t('simulation.subtitlePerChip') }}
                                    </div>
                                </div>
                                <img
                                    alt="radio-button"
                                    v-if="personalize === false"
                                    src="~/assets/img/circle-dot.svg"
                                />
                                <img
                                    alt="radio-button"
                                    v-else
                                    src="~/assets/img/circle.svg"
                                />
                            </div>
                        </div>

                        <div class="buttons">
                            <button class="secondary button" @click="step--">
                                {{ $t('simulation.back') }}
                            </button>
                            <button
                                class="primary button"
                                :class="{ disabled: personalize === null }"
                                @click="step++"
                            >
                                {{ $t('simulation.next') }}
                            </button>
                        </div>
                    </div>

                    <!-- STEP 5 -->
                    <div class="question-group" :class="{ coming: step < 5, done: step > 5 }">
                        <div class="question">
                            <span class="step">{{ $t('simulation.stepXofY', {x: 5, y: 6}) }}</span>
                            <span class="heading-sm title">
                                {{ $t('simulation.questionDeviceCount') }}
                            </span>
                        </div>

                        <div class="answers">
                            <div class="input-group">
                                <label>{{ $t('simulation.deviceCountLabel') }}</label>
                                <input
                                    type="number"
                                    v-model="deviceCount"
                                    min="0" inputmode="numeric" pattern="[0-9]*"
                                    :placeholder="$t('simulation.estimatePlaceholder')"
                                />
                            </div>
                        </div>

                        <div class="error" v-if="error === 'DEVICE_COUNT'">
                            {{ $t('simulation.errors.deviceCount') }}
                        </div>

                        <div class="buttons">
                            <button class="secondary button" @click="step--">
                                {{ $t('simulation.back') }}
                            </button>
                            <button class="primary button" @click="validateDeviceCount()">
                                {{ $t('simulation.next') }}
                            </button>
                        </div>
                    </div>

                    <!-- STEP 6 -->
                    <div class="question-group" :class="{ coming: step < 6, done: step > 6 }">
                        <div class="question">
                            <span class="step"> {{ $t('simulation.stepXofY', {x: 6, y: 6}) }}</span>
                            <span class="heading-sm title">{{ $t('simulation.questionAdditional') }}</span>
                        </div>

                        <div class="answers">
                            <div class="answer" @click="training = !training">
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">{{ $t('simulation.training.title') }}</div>
                                    <div class="subtitle">{{ $t('simulation.training.subtitle') }}</div>
                                </div>
                                <img
                                    v-if="!training"
                                    alt="radio-button"
                                    src="~/assets/img/square.svg"
                                />
                                <img
                                    v-else
                                    alt="radio-button"
                                    src="~/assets/img/square-check.svg"
                                />
                            </div>

                            <div class="answer" @click="helpCenter = !helpCenter">
                                <div class="icon"></div>
                                <div class="content">
                                    <div class="title">
                                        {{ $t('simulation.helpCenter.title') }}
                                    </div>
                                    <div class="subtitle">
                                        {{ $t('simulation.helpCenter.subtitle') }}
                                    </div>
                                </div>
                                <img
                                    v-if="!helpCenter"
                                    alt="radio-button"
                                    src="~/assets/img/square.svg"
                                />
                                <img
                                    v-else
                                    alt="radio-button"
                                    src="~/assets/img/square-check.svg"
                                />
                            </div>
                        </div>

                        <div class="buttons">
                            <button class="secondary button" @click="step--">
                                {{ $t('simulation.back') }}
                            </button>
                            <button class="primary button" @click="step++">
                                {{ $t('simulation.next') }}
                            </button>
                        </div>
                    </div>

                    <!-- STEP 7: CONTACT -->
                    <div class="question-group" :class="{ coming: step < 7, done: step > 7 }">
                        <div class="question">
                            <span class="step">{{ $t('simulation.contactStep') }}</span>
                            <span class="heading-sm title">
                                {{ $t('simulation.questionContact') }}
                            </span>
                        </div>

                        <div class="answers">
                            <div class="input-group">
                                <label>{{ $t('simulation.contactEmailLabel') }}</label>
                                <input
                                    v-model="contactEmail"
                                    :placeholder="$t('simulation.contactEmailPlaceholder')"
                                />
                            </div>

                            <div class="input-group">
                                <label>{{ $t('simulation.contactPhoneLabel') }}</label>
                                <input
                                    v-model="contactPhone"
                                    min="0" inputmode="numeric" pattern="[0-9]*"
                                    :placeholder="$t('simulation.contactPhonePlaceholder')"
                                />
                            </div>
                        </div>

                        <div class="error" v-if="error === 'EMAIL'">
                            {{ $t('simulation.errors.email') }}
                        </div>

                        <div class="error" v-if="error === 'PHONE'">
                            {{ $t('simulation.errors.phone') }}
                        </div>

                        <button class="primary button" @click="validateContactData()">
                            {{ $t('simulation.seeEstimation') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- RESULTS -->
            <div class="content" v-else>
                <div class="result">
                    <div class="content-title">
                        <span class="small">{{ $t('simulation.resultTitle') }}</span>
                        <span class="heading-xl">
                            {{ $t('simulation.resultSubtitle') }}
                        </span>
                    </div>

                    <div class="result-grid">
                        <div class="item">
                            <span class="amount">
                                {{ getSimulationResults().fees }}€
                            </span>
                            <span class="subtitle">
                                {{ $t('simulation.resultFees') }}
                            </span>
                        </div>

                        <div class="item">
                            <span class="amount">
                                {{ getSimulationResults().rfidChips }}€
                            </span>
                            <span class="subtitle">
                                {{ $t('simulation.resultChips') }}
                            </span>
                        </div>

                        <div class="item">
                            <span class="amount">
                                {{ getSimulationResults().fees }}€
                            </span>
                            <span class="subtitle">
                                {{ $t('simulation.resultInstallation') }}
                            </span>
                        </div>

                        <div class="item" v-if="needsType === 'PERMANENT'">
                            <span class="amount">
                                {{ getSimulationResults().subscription }}€
                                <span class="small">/ {{ $t('simulation.month') }}</span>
                            </span>
                            <span class="subtitle">
                                {{ $t('simulation.resultSubscription') }}
                            </span>
                        </div>
                    </div>

                    <NuxtLinkLocale to="contact" class="primary button">
                        {{ $t('simulation.contactUs') }}
                        <img
                            class="white-icon"
                            alt="radio-button"
                            src="~/assets/img/chevron-right.svg"
                        />
                    </NuxtLinkLocale>
                </div>
            </div>
        </div>
    </div>
</template>
