<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";
import {UserIdRepository} from "../class/UserIdRepository.ts";

const i18n = useI18n();

useHead({
    title: i18n.t('contact.seo.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('contact.seo.description')
        },
        {
            property: 'og:title',
            content: i18n.t('contact.seo.title')
        },
        {
            property: 'og:description',
            content: i18n.t('contact.seo.description')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ]
});

let contactData = reactive({
    email: '',
    phone: '',
    company: '',
    content: ''
})

let sending = ref(false);
let sent = ref(false);

function verifyForm() {
    return !(!contactData.email || !contactData.phone || !contactData.content);

}

async function sendToAirtable() {
    if(!verifyForm()) return;
    sending.value = true;
    try {
        const userIdRepository = new UserIdRepository();
        await fetch('https://pro.weecop.fr/cashl.es/contact.php', {
            method: 'POST',
            body: JSON.stringify({
                'fields': {
                    'fldzk3R5czs1YM5Mq': userIdRepository.getUserId(),
                    'fldeyo7ygnqmaGzrM': contactData.email,
                    'fldPOdaYUyM4mFNQy': contactData.phone,
                    'fld0VkSBFuYE3Sock': contactData.company,
                    'fld9n1mXXNaSbKg8G': contactData.content,
                    'fldCCllvX2cTVFE9H': 'Cashl.es',
                }
            })
        });

        sent.value = true;
        Object.assign(contactData, {
            email: '',
            phone: '',
            company: '',
            content: ''
        });
    } catch(err) {}

    sending.value = false;
}

</script>

<style lang="sass">
@use '../pagesSrc/contact/contact.scss' as *
</style>

<template>
    <div id="contact-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="first container">
            <div class="content">
                <div class="grid">
                    <div class="form">
                        <h1 class="heading-xl">{{ $t('contact.form.title') }}</h1>

                        <div class="input-group">
                            <label>{{ $t('contact.form.email.label') }}</label>
                            <input v-model="contactData.email" :placeholder="$t('contact.form.email.placeholder')" type="text" />
                        </div>

                        <div class="input-group">
                            <label>{{ $t('contact.form.phone.label') }}</label>
                            <input v-model="contactData.phone" :placeholder="$t('contact.form.phone.placeholder')" type="text" />
                        </div>

                        <div class="input-group">
                            <label>{{ $t('contact.form.organization.label') }}</label>
                            <input v-model="contactData.company" :placeholder="$t('contact.form.organization.placeholder')" type="text" />
                        </div>

                        <div class="input-group">
                            <label>{{ $t('contact.form.help.label') }}</label>
                            <textarea v-model="contactData.content" :placeholder="$t('contact.form.help.placeholder')"></textarea>
                        </div>

                        <button v-if="!sent" class="primary button" :class="{disabled: !verifyForm() || sending}" @click="sendToAirtable();">
                            {{ $t('contact.form.submit') }}
                        </button>
                        <div class="sent" v-else>
                            {{ $t('contact.form.sent') }}
                        </div>
                    </div>

                    <div class="contacts">
                        <div class="contact-method">
                            <span class="method-title" v-if="$t('contact.callUs.number')">{{ $t('contact.callUs.title') }}</span>
                            <a :href="`tel:${$t('contact.callUs.number')}`" class="contact">{{ $t('contact.callUs.number') }}</a>
                        </div>

                        <div class="contact-method">
                            <span class="method-title">{{ $t('contact.emailUs.title') }}</span>
                            <a :href="`mailto:${$t('contact.emailUs.address')}`" class="contact">{{ $t('contact.emailUs.address') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>