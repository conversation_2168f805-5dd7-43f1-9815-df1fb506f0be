<script setup>
import { computed } from 'vue'

const props = defineProps(['title', 'description', 'horizontal', 'headingLevel'])

// Default to h3 if no heading level is specified
const headingTag = computed(() => props.headingLevel || 'h3')
</script>

<template>
    <div class="card-content-component" :class="{horizontal: horizontal}">

        <div class="top">
            <component :is="headingTag" class="heading-sm title"> {{ props.title }}</component>
            <span class="description" v-html="props.description"></span>
        </div>
        <div class="bottom">
            <slot></slot>
        </div>
    </div>
</template>

<style lang="scss">


    .cards {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        margin-top: 4rem;
    }

    .two-cards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 2rem;

        @media (max-width: 1100px) {
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
        }
    }

    .card-content-component {
        display: flex;
        flex-direction: column;
        border-radius: 25px;
        overflow: hidden;

        .top {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 3rem;
            background: var(--soft-background-color);
            flex-grow: 2;

            @media (max-width: 1100px) {
                padding: 2rem;
            }
        }

        .bottom {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            height: 300px;
            background: var(--soft-background-color);

            img {
                object-fit: cover;
                max-width: 100%;
                max-height: 100%;
            }
        }

        &.horizontal {
            @media (min-width: 1100px) {
                display: grid;
                grid-template-columns: 1fr 1fr;

                .top {
                    justify-content: center;
                }

                .bottom {
                    height: 450px;
                }
            }
        }
    }

</style>