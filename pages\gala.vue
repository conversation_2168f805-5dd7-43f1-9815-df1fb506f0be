<script setup>
import NavbarComponent from "~/components/NavbarComponent/NavbarComponent.vue";
import FooterComponent from "~/components/FooterComponent/FooterComponent.vue";
import TextImageContainerComponent from "~/components/TextImageContainerComponent/TextImageContainerComponent.vue";

const i18n = useI18n();

useHead({
    title: i18n.t('gala.hero.title'),
    meta: [
        {
            name: 'description',
            content: i18n.t('gala.hero.subtitle')
        },
        {
            property: 'og:title',
            content: i18n.t('gala.hero.title')
        },
        {
            property: 'og:description',
            content: i18n.t('gala.hero.subtitle')
        },
        {
            property: 'og:type',
            content: 'website'
        }
    ]
});

</script>

<style lang="sass">
@use '../pagesSrc/gala/gala.scss' as *
</style>

<template>
    <div id="pool-page" class="page">
        <NavbarComponent></NavbarComponent>

        <div class="first container primary">
            <div class="content">
                <div class="hero-banner">
                    <div class="left">
                        <div>
                            <h1 class="title heading-2xl">{{ $t('gala.hero.title') }}</h1>
                        </div>

                        <h2 class="subtitle heading-sm">{{ $t('gala.hero.subtitle') }}</h2>

                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <div class="trusted-by-companies">
                            <span class="title">{{ $t('gala.hero.trustedBy') }}</span>

                            <div class="companies">
                                <NuxtImg format="webp" alt="INSA" src="/insa.png" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="EEIGM" src="/eeigm-big.jpeg" sizes="xs:150px md:150px" />
                                <NuxtImg format="webp" alt="Polytech" src="/polytech.png" sizes="xs:150px md:150px" />
                            </div>
                        </div>
                    </div>

                    <div class="illustration">
                        <NuxtImg format="webp" alt="pool-illustration" width="579" height="549" :src="'/gala-' + $i18n.locale + '.png'" sizes="xs:420px md:600px" />
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="text-images">
                    <TextImageContainerComponent
                        :small="$t('gala.features.fluidity.small')"
                        :title="$t('gala.features.fluidity.title')"
                        :subtitle="$t('gala.features.fluidity.subtitle')"
                        heading-level="h2"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/gala-eat.jpg" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('gala.features.organization.small')"
                        :title="$t('gala.features.organization.title')"
                        :subtitle="$t('gala.features.organization.subtitle')"
                        :reversed="true"
                        heading-level="h2"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/interface-stats.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>

                    <TextImageContainerComponent
                        :small="$t('gala.features.accessibility.small')"
                        :title="$t('gala.features.accessibility.title')"
                        :subtitle="$t('gala.features.accessibility.subtitle')"
                        heading-level="h2"
                    >
                        <NuxtImg loading="lazy" alt="placeholder" format="webp" width="600" height="450" src="/interface-point-of-sale.png" sizes="xs:420px md:600px" />
                    </TextImageContainerComponent>
                </div>
            </div>
        </div>

        <div class="container primary">
            <div class="content">
                <div class="content-title">
                    <span class="small">{{ $t('gala.clients.titleSmall') }}</span>
                    <h2 class="heading-xl">{{ $t('gala.clients.title') }}</h2>
                </div>

                <div class="testimonials">
                    <div class="testimonial">
                        <div class="comment">{{ $t('gala.clients.testimonial1.comment') }}</div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="testimonial1" width="36" height="36" src="/polytech.png" sizes="xs:36px md:36px" />
                                <div class="data">
                                    <span class="name">{{ $t('gala.clients.testimonial1.name') }}</span>
                                    <span class="company">{{ $t('gala.clients.testimonial1.company') }}</span>
                                </div>
                            </div>
                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" v-for="i in 5" :key="i" />
                            </div>
                        </div>
                    </div>

                    <div class="testimonial">
                        <div class="comment">{{ $t('gala.clients.testimonial2.comment') }}</div>
                        <div class="bottom">
                            <div class="client-infos">
                                <NuxtImg format="webp" loading="lazy" alt="testimonial2" width="36" height="36" src="/eeigm.jpeg" sizes="xs:36px md:36px" />
                                <div class="data">
                                    <span class="name">{{ $t('gala.clients.testimonial2.name') }}</span>
                                    <span class="company">{{ $t('gala.clients.testimonial2.company') }}</span>
                                </div>
                            </div>
                            <div class="stars">
                                <img src="~/assets/img/star.svg" alt="star" class="icon" v-for="i in 5" :key="i" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <div class="content-bubble">
                    <div class="content-title">
                        <h2 class="heading-xl">{{ $t('gala.readyToSwitch.title') }}</h2>
                    </div>

                    <div class="big-buttons">
                        <NuxtLinkLocale to="/simulation" class="primary button">
                            {{ $t('home_hero.start_quote') }}
                            <div class="hint">
                                <img src="~/assets/img/clock.svg" alt="clock" />
                                {{ $t('home_hero.quote_duration') }}
                            </div>
                        </NuxtLinkLocale>

                        <NuxtLinkLocale to="/contact" class="grey button">
                            {{ $t('gala.readyToSwitch.contact') }}
                        </NuxtLinkLocale>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="content">
                <FooterComponent></FooterComponent>
            </div>
        </div>
    </div>
</template>
